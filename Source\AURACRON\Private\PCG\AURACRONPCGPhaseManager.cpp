// AURACRONPCGPhaseManager.cpp
// Implementação Consolidada do Sistema de Fases Evolutivas - UE 5.6
// CONSOLIDADO: Combina AURACRONPCGPhaseManager e AURACRONMapPhaseManager

#include "PCG/AURACRONPCGPhaseManager.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGLaneSystem.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "PCG/AURACRONPCGUtility.h"
#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGTrail.h"
#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "PCG/AURACRONPCGPerformanceManager.h"
#include "PCG/AURACRONPCGEnergyPulse.h"
#include "PCG/AURACRONPCGChaosPortal.h"
#include "Net/UnrealNetwork.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Engine/World.h"
#include "NavigationSystem.h"
#include "EngineUtils.h"
#include "Components/DamageZoneComponent.h"

AAURACRONPCGPhaseManager::AAURACRONPCGPhaseManager()
    : LaneSystem(nullptr)
    , JungleSystem(nullptr)
    , ObjectiveSystem(nullptr)
    , EnvironmentManager(nullptr)
    , bAutoStartProgression(true)
    , bProgressionActive(false)
    , CurrentPhase(EAURACRONMapPhase::Awakening)
    , TargetPhase(EAURACRONMapPhase::Awakening)
    , NextPhase(EAURACRONMapPhase::Convergence)
    , bIsInTransition(false)
    , TotalElapsedTime(0.0f)
    , TimeRemainingInPhase(0.0f)
    , CurrentTransitionDuration(10.0f)
    , TransitionProgress(0.0f)
    , ProgressionSpeedMultiplier(1.0f)
    , RegisteredEnvironmentManager(nullptr)
    , CurrentPhaseTime(0.0f)
    , bIsPaused(false)
    , TransitionDuration(10.0f)
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false);
    
    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
}

void AAURACRONPCGPhaseManager::BeginPlay()
{
    Super::BeginPlay();
    
    // Inicializar sistema apenas no servidor
    if (HasAuthority())
    {
        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle InitTimer;
        GetWorld()->GetTimerManager().SetTimer(InitTimer, this, 
            &AAURACRONPCGPhaseManager::InitializePhaseSystem, 3.0f, false);
    }
}

void AAURACRONPCGPhaseManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (HasAuthority())
    {
        // Atualizar tempo total decorrido
        TotalElapsedTime += DeltaTime * ProgressionSpeedMultiplier;
        
        // Atualizar progressão das fases
        if (bProgressionActive && !bIsInTransition)
        {
            TimeRemainingInPhase -= DeltaTime * ProgressionSpeedMultiplier;
            
            if (TimeRemainingInPhase <= 0.0f)
            {
                StartTransitionToNextPhase();
            }
        }
        
        // Atualizar transição
        if (bIsInTransition)
        {
            ExecuteTransition();
        }
        
        // Atualizar efeitos temporários
        UpdateTemporaryEffects(DeltaTime);
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGPhaseManager::InitializePhaseSystem()
{
    if (!HasAuthority())
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Inicializando sistema de 4 fases evolutivas"));
    
    // Encontrar sistemas integrados
    LaneSystem = Cast<AAURACRONPCGLaneSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGLaneSystem::StaticClass()));
    JungleSystem = Cast<AAURACRONPCGJungleSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGJungleSystem::StaticClass()));
    ObjectiveSystem = Cast<AAURACRONPCGObjectiveSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGObjectiveSystem::StaticClass()));
    EnvironmentManager = Cast<AAURACRONPCGEnvironmentManager>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGEnvironmentManager::StaticClass()));
    
    // Inicializar configurações das fases
    InitializePhaseSettings();
    
    // Configurar fase inicial (Awakening)
    SetupPhase(EAURACRONMapPhase::Awakening);
    
    // Iniciar progressão automática se configurado
    if (bAutoStartProgression)
    {
        StartPhaseProgression();
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Sistema inicializado com sucesso"));
}

void AAURACRONPCGPhaseManager::StartPhaseProgression()
{
    if (!HasAuthority())
    {
        return;
    }
    
    bProgressionActive = true;
    
    // Configurar tempo inicial baseado na fase atual
    const FAURACRONPhaseSettings* CurrentSettings = PhaseSettings.Find(CurrentPhase);
    if (CurrentSettings)
    {
        TimeRemainingInPhase = CurrentSettings->PhaseDuration;
    }
    else
    {
        TimeRemainingInPhase = FAURACRONMapDimensions::PHASE_AWAKENING_DURATION_SECONDS;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Progressão iniciada, próxima fase em %.1fs"), TimeRemainingInPhase);
}

void AAURACRONPCGPhaseManager::StopPhaseProgression()
{
    if (!HasAuthority())
    {
        return;
    }
    
    bProgressionActive = false;
    GetWorld()->GetTimerManager().ClearTimer(PhaseProgressionTimer);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Progressão parada"));
}

void AAURACRONPCGPhaseManager::ForceTransitionToPhase(EAURACRONMapPhase TargetPhaseParam)
{
    if (!HasAuthority() || CurrentPhase == TargetPhaseParam)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Forçando transição para fase %d"), static_cast<int32>(TargetPhaseParam));
    
    TargetPhase = TargetPhaseParam;
    bIsInTransition = true;
    TransitionProgress = 0.0f;
    CurrentTransitionDuration = 10.0f; // Transição forçada mais rápida
    
    // Parar progressão automática temporariamente
    bool bWasProgressionActive = bProgressionActive;
    StopPhaseProgression();
    
    // Reiniciar progressão após transição se estava ativa
    if (bWasProgressionActive)
    {
        FTimerHandle RestartTimer;
        GetWorld()->GetTimerManager().SetTimer(RestartTimer, this, 
            &AAURACRONPCGPhaseManager::StartPhaseProgression, CurrentTransitionDuration + 1.0f, false);
    }
}

EAURACRONMapPhase AAURACRONPCGPhaseManager::GetNextPhase() const
{
    return GetNextPhaseInSequence(CurrentPhase);
}

float AAURACRONPCGPhaseManager::GetTimeRemainingInCurrentPhase() const
{
    return TimeRemainingInPhase;
}

FAURACRONPhaseSettings AAURACRONPCGPhaseManager::GetPhaseSettings(EAURACRONMapPhase Phase) const
{
    const FAURACRONPhaseSettings* Settings = PhaseSettings.Find(Phase);
    return Settings ? *Settings : FAURACRONPhaseSettings();
}

void AAURACRONPCGPhaseManager::ApplyTemporaryPhaseEffect(const FString& EffectName, float Duration)
{
    ActiveTemporaryEffects.Add(EffectName, Duration);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Efeito temporário '%s' aplicado por %.1fs"), *EffectName, Duration);
}

void AAURACRONPCGPhaseManager::AcceleratePhaseProgression(float SpeedMultiplier)
{
    ProgressionSpeedMultiplier = FMath::Max(0.1f, SpeedMultiplier);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Progressão acelerada para %.1fx"), ProgressionSpeedMultiplier);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGPhaseManager::InitializePhaseSettings()
{
    PhaseSettings.Empty();
    
    // FASE 1 - AWAKENING (0-10 minutos)
    FAURACRONPhaseSettings AwakeningSettings;
    AwakeningSettings.Phase = EAURACRONMapPhase::Awakening;
    AwakeningSettings.PhaseName = TEXT("Awakening");
    AwakeningSettings.Description = TEXT("Fase inicial - mapa completo, jungle básico, objetivos inativos");
    AwakeningSettings.PhaseDuration = FAURACRONMapDimensions::PHASE_AWAKENING_DURATION_SECONDS;
    AwakeningSettings.PhaseStartTime = 0.0f;
    AwakeningSettings.MapScaleFactor = 1.0f;
    AwakeningSettings.EnvironmentSpeedMultiplier = 1.0f;
    AwakeningSettings.JungleRespawnMultiplier = 1.0f;
    AwakeningSettings.ObjectiveRespawnMultiplier = 1.0f;
    AwakeningSettings.ObjectiveHealthMultiplier = 1.0f;
    AwakeningSettings.RewardMultiplier = 1.0f;
    AwakeningSettings.bSpecialObjectivesActive = false;
    AwakeningSettings.bChaosIslandsActive = false;
    AwakeningSettings.SpecialEffects.Add(TEXT("SoftLighting"));
    AwakeningSettings.SpecialEffects.Add(TEXT("CalmAmbience"));
    PhaseSettings.Add(EAURACRONMapPhase::Awakening, AwakeningSettings);
    
    // FASE 2 - CONVERGENCE (10-20 minutos)
    FAURACRONPhaseSettings ConvergenceSettings;
    ConvergenceSettings.Phase = EAURACRONMapPhase::Convergence;
    ConvergenceSettings.PhaseName = TEXT("Convergence");
    ConvergenceSettings.Description = TEXT("Objetivos ativam, jungle evolui, ilhas aparecem");
    ConvergenceSettings.PhaseDuration = FAURACRONMapDimensions::PHASE_CONVERGENCE_DURATION_SECONDS;
    ConvergenceSettings.PhaseStartTime = FAURACRONMapDimensions::PHASE_AWAKENING_DURATION_SECONDS;
    ConvergenceSettings.MapScaleFactor = 1.0f;
    ConvergenceSettings.EnvironmentSpeedMultiplier = 1.0f;
    ConvergenceSettings.JungleRespawnMultiplier = 0.9f; // 10% mais rápido
    ConvergenceSettings.ObjectiveRespawnMultiplier = 1.0f;
    ConvergenceSettings.ObjectiveHealthMultiplier = 1.1f; // 10% mais HP
    ConvergenceSettings.RewardMultiplier = 1.2f; // 20% mais recompensas
    ConvergenceSettings.bSpecialObjectivesActive = true;
    ConvergenceSettings.bChaosIslandsActive = false;
    ConvergenceSettings.SpecialEffects.Add(TEXT("EnergyPulses"));
    ConvergenceSettings.SpecialEffects.Add(TEXT("ObjectiveGlow"));
    ConvergenceSettings.UniqueEvents.Add(TEXT("FirstObjectiveSpawn"));
    PhaseSettings.Add(EAURACRONMapPhase::Convergence, ConvergenceSettings);
    
    // FASE 3 - INTENSIFICATION (20-35 minutos)
    FAURACRONPhaseSettings IntensificationSettings;
    IntensificationSettings.Phase = EAURACRONMapPhase::Intensification;
    IntensificationSettings.PhaseName = TEXT("Intensification");
    IntensificationSettings.Description = TEXT("Todos objetivos ativos, jungle elite, eventos especiais");
    IntensificationSettings.PhaseDuration = FAURACRONMapDimensions::PHASE_INTENSIFICATION_DURATION_SECONDS;
    IntensificationSettings.PhaseStartTime = FAURACRONMapDimensions::PHASE_AWAKENING_DURATION_SECONDS + FAURACRONMapDimensions::PHASE_CONVERGENCE_DURATION_SECONDS;
    IntensificationSettings.MapScaleFactor = 1.0f;
    IntensificationSettings.EnvironmentSpeedMultiplier = 1.25f; // 25% mais rápido
    IntensificationSettings.JungleRespawnMultiplier = 0.8f; // 20% mais rápido
    IntensificationSettings.ObjectiveRespawnMultiplier = 0.85f; // 15% mais rápido
    IntensificationSettings.ObjectiveHealthMultiplier = 1.3f; // 30% mais HP
    IntensificationSettings.RewardMultiplier = 1.5f; // 50% mais recompensas
    IntensificationSettings.bSpecialObjectivesActive = true;
    IntensificationSettings.bChaosIslandsActive = true;
    IntensificationSettings.SpecialEffects.Add(TEXT("IntenseAura"));
    IntensificationSettings.SpecialEffects.Add(TEXT("ChaosPortals"));
    IntensificationSettings.SpecialEffects.Add(TEXT("EliteMonsters"));
    IntensificationSettings.UniqueEvents.Add(TEXT("ChaosIslandActivation"));
    IntensificationSettings.UniqueEvents.Add(TEXT("EliteJungleSpawn"));
    PhaseSettings.Add(EAURACRONMapPhase::Intensification, IntensificationSettings);
    
    // FASE 4 - RESOLUTION (35+ minutos)
    FAURACRONPhaseSettings ResolutionSettings;
    ResolutionSettings.Phase = EAURACRONMapPhase::Resolution;
    ResolutionSettings.PhaseName = TEXT("Resolution");
    ResolutionSettings.Description = TEXT("Mapa contrai 20%, objetivos super poderosos, eventos game-changing");
    ResolutionSettings.PhaseDuration = 0.0f; // Duração indefinida
    ResolutionSettings.PhaseStartTime = FAURACRONMapDimensions::PHASE_RESOLUTION_START_SECONDS;
    ResolutionSettings.MapScaleFactor = FAURACRONMapDimensions::MAP_CONTRACTION_FACTOR; // 20% menor
    ResolutionSettings.EnvironmentSpeedMultiplier = 1.5f; // 50% mais rápido
    ResolutionSettings.JungleRespawnMultiplier = 0.6f; // 40% mais rápido
    ResolutionSettings.ObjectiveRespawnMultiplier = 0.7f; // 30% mais rápido
    ResolutionSettings.ObjectiveHealthMultiplier = 1.5f; // 50% mais HP
    ResolutionSettings.RewardMultiplier = 2.0f; // 100% mais recompensas
    ResolutionSettings.bSpecialObjectivesActive = true;
    ResolutionSettings.bChaosIslandsActive = true;
    ResolutionSettings.SpecialEffects.Add(TEXT("MapContraction"));
    ResolutionSettings.SpecialEffects.Add(TEXT("CriticalAura"));
    ResolutionSettings.SpecialEffects.Add(TEXT("LegendaryMonsters"));
    ResolutionSettings.SpecialEffects.Add(TEXT("EnergyPulses"));
    ResolutionSettings.SpecialEffects.Add(TEXT("ChaosPortals"));
    ResolutionSettings.UniqueEvents.Add(TEXT("MapContractionEvent"));
    ResolutionSettings.UniqueEvents.Add(TEXT("LegendaryObjectiveSpawn"));
    ResolutionSettings.UniqueEvents.Add(TEXT("FinalBattleEffects"));
    PhaseSettings.Add(EAURACRONMapPhase::Resolution, ResolutionSettings);
    
    // Calcular tempos de início
    CalculatePhaseStartTimes();
}

void AAURACRONPCGPhaseManager::SetupPhase(EAURACRONMapPhase Phase)
{
    CurrentPhase = Phase;

    // Aplicar efeitos da fase
    ApplyPhaseEffects(Phase);

    // Notificar sistemas integrados
    NotifySystemsOfPhaseChange(Phase);

    // Ativar eventos únicos da fase
    ActivatePhaseUniqueEvents(Phase);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Fase %d configurada"), static_cast<int32>(Phase));
}

void AAURACRONPCGPhaseManager::StartTransitionToNextPhase()
{
    EAURACRONMapPhase NextPhaseInSequence = GetNextPhaseInSequence(CurrentPhase);

    // Não transicionar se já estamos na última fase
    if (CurrentPhase == EAURACRONMapPhase::Resolution)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Já na fase final (Resolution)"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Iniciando transição de %d para %d"),
        static_cast<int32>(CurrentPhase), static_cast<int32>(NextPhaseInSequence));

    TargetPhase = NextPhaseInSequence;
    NextPhase = NextPhaseInSequence;
    bIsInTransition = true;
    TransitionProgress = 0.0f;
    CurrentTransitionDuration = 15.0f; // 15 segundos de transição
}

void AAURACRONPCGPhaseManager::ExecuteTransition()
{
    // Atualizar progresso da transição
    TransitionProgress += GetWorld()->GetDeltaSeconds() / CurrentTransitionDuration;
    TransitionProgress = FMath::Clamp(TransitionProgress, 0.0f, 1.0f);

    // Aplicar efeitos de transição gradual
    const FAURACRONPhaseSettings* CurrentSettings = PhaseSettings.Find(CurrentPhase);
    const FAURACRONPhaseSettings* TargetSettings = PhaseSettings.Find(TargetPhase);

    if (CurrentSettings && TargetSettings)
    {
        FAURACRONPhaseSettings BlendedSettings = InterpolatePhaseSettings(*CurrentSettings, *TargetSettings, TransitionProgress);

        // Aplicar configurações interpoladas aos sistemas
        if (EnvironmentManager)
        {
            EnvironmentManager->UpdateForMapPhase(TargetPhase);
        }
    }

    // Finalizar transição quando completa
    if (TransitionProgress >= 1.0f)
    {
        CompleteTransition();
    }
}

void AAURACRONPCGPhaseManager::CompleteTransition()
{
    CurrentPhase = TargetPhase;
    bIsInTransition = false;
    TransitionProgress = 0.0f;

    // Configurar fase completamente
    SetupPhase(CurrentPhase);

    // Reiniciar timer para próxima fase se progressão estiver ativa
    if (bProgressionActive)
    {
        const FAURACRONPhaseSettings* Settings = PhaseSettings.Find(CurrentPhase);
        if (Settings && Settings->PhaseDuration > 0.0f)
        {
            TimeRemainingInPhase = Settings->PhaseDuration;
        }
        else
        {
            // Fase Resolution não tem duração definida
            TimeRemainingInPhase = 0.0f;
            bProgressionActive = false;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Transição completa para fase %d"), static_cast<int32>(CurrentPhase));
}

void AAURACRONPCGPhaseManager::ApplyPhaseEffects(EAURACRONMapPhase Phase)
{
    const FAURACRONPhaseSettings* Settings = PhaseSettings.Find(Phase);
    if (!Settings)
    {
        return;
    }

    // Aplicar contração do mapa se necessário (fase Resolution)
    if (Phase == EAURACRONMapPhase::Resolution)
    {
        ApplyMapContraction(Settings->MapScaleFactor);
    }

    // Aplicar efeitos especiais
    for (const FString& Effect : Settings->SpecialEffects)
    {
        if (Effect == TEXT("MapContraction"))
        {
            ApplyMapContraction(Settings->MapScaleFactor);
        }
        else if (Effect == TEXT("EnergyPulses"))
        {
            SpawnEnergyPulses(Settings->PhaseIntensity);
        }
        else if (Effect == TEXT("ChaosPortals"))
        {
            SpawnChaosPortals(Settings->PhaseIntensity);
        }
        // Adicionar mais efeitos conforme necessário
    }
}

void AAURACRONPCGPhaseManager::NotifySystemsOfPhaseChange(EAURACRONMapPhase NewPhase)
{
    // Notificar sistema de lanes
    if (LaneSystem)
    {
        LaneSystem->UpdateForMapPhase(NewPhase);
    }

    // Notificar sistema de jungle
    if (JungleSystem)
    {
        JungleSystem->UpdateForMapPhase(NewPhase);
    }

    // Notificar sistema de objetivos
    if (ObjectiveSystem)
    {
        ObjectiveSystem->UpdateForMapPhase(NewPhase);
    }

    // Notificar gerenciador de ambientes
    if (EnvironmentManager)
    {
        EnvironmentManager->UpdateForMapPhase(NewPhase);
    }
}

EAURACRONMapPhase AAURACRONPCGPhaseManager::GetNextPhaseInSequence(EAURACRONMapPhase Current) const
{
    // Sequência: Awakening → Convergence → Intensification → Resolution
    switch (Current)
    {
    case EAURACRONMapPhase::Awakening:
        return EAURACRONMapPhase::Convergence;
    case EAURACRONMapPhase::Convergence:
        return EAURACRONMapPhase::Intensification;
    case EAURACRONMapPhase::Intensification:
        return EAURACRONMapPhase::Resolution;
    case EAURACRONMapPhase::Resolution:
        return EAURACRONMapPhase::Resolution; // Permanece na última fase
    default:
        return EAURACRONMapPhase::Awakening;
    }
}

void AAURACRONPCGPhaseManager::ApplyMapContraction(float ContractionFactor)
{
    // Aplicar contração do mapa (20% menor na fase Resolution)
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Aplicando contração do mapa: %.1f%%"), (1.0f - ContractionFactor) * 100.0f);

    // Obter referência ao gerenciador de performance
    AAURACRONPCGPerformanceManager* PerformanceManager = nullptr;
    if (GetWorld())
    {
        for (TActorIterator<AAURACRONPCGPerformanceManager> It(GetWorld()); It; ++It)
        {
            PerformanceManager = *It;
            break;
        }
    }

    // Ajustar fator de contração baseado na performance do hardware
    float AdjustedContractionFactor = ContractionFactor;
    if (PerformanceManager)
    {
        // Obter métricas de performance atuais
        FAURACRONPCGPerformanceMetrics Metrics = PerformanceManager->GetPerformanceMetrics();
        
        // Ajustar contração baseada no FPS atual
        // Se o FPS estiver abaixo do alvo, aumentar a contração para melhorar performance
        if (Metrics.CurrentFPS < PerformanceManager->GetTargetFPS() * 0.8f) // FPS 20% abaixo do alvo
        {
            // Aumentar contração (mapa menor) para hardware de baixo desempenho
            AdjustedContractionFactor = FMath::Max(ContractionFactor - 0.1f, 0.6f); // Até 40% de contração
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Aumentando contração para %.1f%% devido a baixo FPS: %.1f"),
                   (1.0f - AdjustedContractionFactor) * 100.0f, Metrics.CurrentFPS);
        }
        else if (Metrics.CurrentFPS > PerformanceManager->GetTargetFPS() * 1.2f) // FPS 20% acima do alvo
        {
            // Reduzir contração (mapa maior) para hardware de alto desempenho
            AdjustedContractionFactor = FMath::Min(ContractionFactor + 0.05f, 0.9f); // No máximo 10% de contração
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Reduzindo contração para %.1f%% devido a alto FPS: %.1f"),
                   (1.0f - AdjustedContractionFactor) * 100.0f, Metrics.CurrentFPS);
        }
    }

    // Implementar contração física do mapa
    // 1. Reduzir área jogável
    float OriginalRadius = FAURACRONMapDimensions::MAP_RADIUS_CM;
    float ContractedRadius = OriginalRadius * AdjustedContractionFactor;
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    
    // 2. Mover elementos para dentro da nova área
    // Processar atores de ambiente
    for (AAURACRONPCGEnvironment* Environment : PCGActorReferences.EnvironmentActors)
    {
        if (Environment)
        {
            // Calcular distância atual do centro
            FVector ActorLocation = Environment->GetActorLocation();
            ActorLocation.Z = 0; // Ignorar altura para cálculo de distância 2D
            FVector CenterToActor = ActorLocation - MapCenter;
            float CurrentDistance = CenterToActor.Size2D();

            // Se o ator estiver fora do novo raio contraído
            if (CurrentDistance > ContractedRadius)
            {
                // Calcular nova posição dentro da área contraída
                FVector Direction = CenterToActor.GetSafeNormal2D();
                FVector NewLocation = MapCenter + Direction * (ContractedRadius * 0.95f); // 5% de margem

                // Preservar altura original
                NewLocation.Z = ActorLocation.Z;

                // Mover o ator para a nova posição
                Environment->SetActorLocation(NewLocation);

                UE_LOG(LogTemp, Verbose, TEXT("AURACRONPCGPhaseManager: Movendo ambiente %s para dentro da área contraída"),
                       *Environment->GetName());
            }
        }
    }

    // Processar atores de trilha
    for (AAURACRONPCGTrail* Trail : PCGActorReferences.TrailActors)
    {
        if (Trail)
        {
            // Calcular distância atual do centro
            FVector ActorLocation = Trail->GetActorLocation();
            ActorLocation.Z = 0; // Ignorar altura para cálculo de distância 2D
            FVector CenterToActor = ActorLocation - MapCenter;
            float CurrentDistance = CenterToActor.Size2D();

            // Se o ator estiver fora do novo raio contraído
            if (CurrentDistance > ContractedRadius)
            {
                // Calcular nova posição dentro da área contraída
                FVector Direction = CenterToActor.GetSafeNormal2D();
                FVector NewLocation = MapCenter + Direction * (ContractedRadius * 0.95f); // 5% de margem

                // Preservar altura original
                NewLocation.Z = ActorLocation.Z;

                // Mover o ator para a nova posição
                Trail->SetActorLocation(NewLocation);

                UE_LOG(LogTemp, Verbose, TEXT("AURACRONPCGPhaseManager: Movendo trilha %s para dentro da área contraída"),
                       *Trail->GetName());
            }
        }
    }

    // Processar atores de ilha
    for (AAURACRONPCGIsland* Island : PCGActorReferences.IslandActors)
    {
        if (Island)
        {
            // Calcular distância atual do centro
            FVector ActorLocation = Island->GetActorLocation();
            ActorLocation.Z = 0; // Ignorar altura para cálculo de distância 2D
            FVector CenterToActor = ActorLocation - MapCenter;
            float CurrentDistance = CenterToActor.Size2D();

            // Se o ator estiver fora do novo raio contraído
            if (CurrentDistance > ContractedRadius)
            {
                // Calcular nova posição dentro da área contraída
                FVector Direction = CenterToActor.GetSafeNormal2D();
                FVector NewLocation = MapCenter + Direction * (ContractedRadius * 0.95f); // 5% de margem

                // Preservar altura original
                NewLocation.Z = ActorLocation.Z;

                // Mover o ator para a nova posição
                Island->SetActorLocation(NewLocation);

                UE_LOG(LogTemp, Verbose, TEXT("AURACRONPCGPhaseManager: Movendo ilha %s para dentro da área contraída"),
                       *Island->GetName());
            }
        }
    }

    // Processar Prismal Flow se existir
    if (PCGActorReferences.PrismalFlowActor)
    {
        AAURACRONPCGPrismalFlow* PrismalFlow = PCGActorReferences.PrismalFlowActor;

        // Calcular distância atual do centro
        FVector ActorLocation = PrismalFlow->GetActorLocation();
        ActorLocation.Z = 0; // Ignorar altura para cálculo de distância 2D
        FVector CenterToActor = ActorLocation - MapCenter;
        float CurrentDistance = CenterToActor.Size2D();

        // Se o ator estiver fora do novo raio contraído
        if (CurrentDistance > ContractedRadius)
        {
            // Calcular nova posição dentro da área contraída
            FVector Direction = CenterToActor.GetSafeNormal2D();
            FVector NewLocation = MapCenter + Direction * (ContractedRadius * 0.95f); // 5% de margem

            // Preservar altura original
            NewLocation.Z = ActorLocation.Z;

            // Mover o ator para a nova posição
            PrismalFlow->SetActorLocation(NewLocation);

            UE_LOG(LogTemp, Verbose, TEXT("AURACRONPCGPhaseManager: Movendo Prismal Flow %s para dentro da área contraída"),
                   *PrismalFlow->GetName());
        }
    }
    
    // 3. Configurar dano a jogadores fora da zona segura
    SetupOutOfBoundsDamage(ContractedRadius);
    
    // 4. Atualizar sistemas de navegação
    UpdateNavigationSystem(ContractedRadius);
    
    // 5. Notificar sistemas relacionados sobre a contração
    NotifySystemsOfMapContraction(AdjustedContractionFactor);
    
    // Disparar evento de contração do mapa
    OnMapContraction.Broadcast(AdjustedContractionFactor, ContractedRadius);
}

void AAURACRONPCGPhaseManager::SetupOutOfBoundsDamage(float SafeRadius)
{
    // Configurar sistema de dano para jogadores que saem da área segura
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Configurando dano fora da zona segura com raio %.1f"), SafeRadius);
    
    // Encontrar componente de dano ou criar se não existir
    UDamageZoneComponent* DamageZone = FindComponentByClass<UDamageZoneComponent>();
    if (!DamageZone)
    {
        // Criar o componente de zona de dano se não existir
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Criando componente de zona de dano"));
        DamageZone = NewObject<UDamageZoneComponent>(this, UDamageZoneComponent::StaticClass(), TEXT("DamageZone"));
        DamageZone->RegisterComponent();
        
        if (!DamageZone)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRONPCGPhaseManager: Falha ao criar componente de zona de dano"));
            return;
        }
    }
    

    // Configurar parâmetros da zona de dano
    DamageZone->SetSafeRadius(SafeRadius);
    DamageZone->SetDamagePerSecond(10.0f); // Dano base por segundo
    DamageZone->SetDamageScalingFactor(1.5f); // Escala de aumento do dano com o tempo
    DamageZone->SetWarningRadius(SafeRadius * 1.05f); // 5% de margem para aviso
    DamageZone->SetActive(true);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Zona de dano configurada com sucesso"));
}

void AAURACRONPCGPhaseManager::UpdateNavigationSystem(float NewRadius)
{
    // Atualizar sistema de navegação para refletir a nova área jogável
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Atualizando sistema de navegação para raio %.1f"), NewRadius);
    
    UNavigationSystemV1* NavSystem = FNavigationSystem::GetCurrent<UNavigationSystemV1>(GetWorld());
    if (!NavSystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGPhaseManager: Sistema de navegação não encontrado"));
        return;
    }
    
    // Definir limites de navegação baseados no novo raio
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    FBox NavigationBounds(MapCenter - FVector(NewRadius, NewRadius, 1000.0f),
                          MapCenter + FVector(NewRadius, NewRadius, 1000.0f));
    
    // Atualizar limites de navegação
    NavSystem->SetBuildBounds(NavigationBounds);
    
    // Reconstruir áreas afetadas
    NavSystem->Build();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Sistema de navegação atualizado com sucesso"));
}

void AAURACRONPCGPhaseManager::NotifySystemsOfMapContraction(float ContractionFactor)
{
    // Notificar todos os sistemas relacionados sobre a contração do mapa
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Notificando sistemas sobre contração do mapa: %.1f%%"), 
           (1.0f - ContractionFactor) * 100.0f);
    
    // Notificar sistema de lanes
    if (LaneSystem)
    {
        LaneSystem->OnMapContraction(ContractionFactor);
    }
    
    // Notificar sistema de jungle
    if (JungleSystem)
    {
        JungleSystem->OnMapContraction(ContractionFactor);
    }
    
    // Notificar sistema de objetivos
    // TODO: Implementar OnMapContraction no ObjectiveSystem
    if (ObjectiveSystem)
    {
        UE_LOG(LogTemp, Log, TEXT("OnMapContraction - ObjectiveSystem contraction not yet implemented"));
    }

    // Notificar gerenciador de ambientes
    // TODO: Implementar OnMapContraction no EnvironmentManager
    if (EnvironmentManager)
    {
        UE_LOG(LogTemp, Log, TEXT("OnMapContraction - EnvironmentManager contraction not yet implemented"));
    }
    
    // Notificar outros sistemas PCG
    for (AAURACRONPCGEnvironment* Environment : PCGActorReferences.EnvironmentActors)
    {
        if (IsValid(Environment))
        {
            // Chamar função pública OnMapContraction
            Environment->OnMapContraction(ContractionFactor);
        }
    }
    
    for (AAURACRONPCGTrail* Trail : PCGActorReferences.TrailActors)
    {
        if (IsValid(Trail))
        {
            Trail->OnMapContraction(ContractionFactor);
        }
    }
    
    for (AAURACRONPCGIsland* Island : PCGActorReferences.IslandActors)
    {
        if (IsValid(Island))
        {
            Island->OnMapContraction(ContractionFactor);
        }
    }
    
    if (IsValid(PCGActorReferences.PrismalFlowActor))
    {
        PCGActorReferences.PrismalFlowActor->OnMapContraction(ContractionFactor);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Todos os sistemas notificados sobre a contração"));
}

void AAURACRONPCGPhaseManager::UpdateTemporaryEffects(float DeltaTime)
{
    TArray<FString> ExpiredEffects;

    for (auto& EffectPair : ActiveTemporaryEffects)
    {
        EffectPair.Value -= DeltaTime;

        if (EffectPair.Value <= 0.0f)
        {
            ExpiredEffects.Add(EffectPair.Key);
        }
    }

    // Remover efeitos expirados
    for (const FString& ExpiredEffect : ExpiredEffects)
    {
        ActiveTemporaryEffects.Remove(ExpiredEffect);
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Efeito temporário '%s' expirou"), *ExpiredEffect);
    }
}

FAURACRONPhaseSettings AAURACRONPCGPhaseManager::InterpolatePhaseSettings(
    const FAURACRONPhaseSettings& From,
    const FAURACRONPhaseSettings& To,
    float Alpha) const
{
    FAURACRONPhaseSettings Result = From;

    // Interpolar valores numéricos
    Result.MapScaleFactor = FMath::Lerp(From.MapScaleFactor, To.MapScaleFactor, Alpha);
    Result.EnvironmentSpeedMultiplier = FMath::Lerp(From.EnvironmentSpeedMultiplier, To.EnvironmentSpeedMultiplier, Alpha);
    Result.JungleRespawnMultiplier = FMath::Lerp(From.JungleRespawnMultiplier, To.JungleRespawnMultiplier, Alpha);
    Result.ObjectiveRespawnMultiplier = FMath::Lerp(From.ObjectiveRespawnMultiplier, To.ObjectiveRespawnMultiplier, Alpha);
    Result.ObjectiveHealthMultiplier = FMath::Lerp(From.ObjectiveHealthMultiplier, To.ObjectiveHealthMultiplier, Alpha);
    Result.RewardMultiplier = FMath::Lerp(From.RewardMultiplier, To.RewardMultiplier, Alpha);

    return Result;
}

void AAURACRONPCGPhaseManager::ActivatePhaseUniqueEvents(EAURACRONMapPhase Phase)
{
    const FAURACRONPhaseSettings* Settings = PhaseSettings.Find(Phase);
    if (!Settings)
    {
        return;
    }

    // Ativar eventos únicos da fase
    for (const FString& Event : Settings->UniqueEvents)
    {
        if (Event == TEXT("FirstObjectiveSpawn"))
        {
            // Ativar primeiro objetivo estratégico
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Ativando primeiro objetivo estratégico"));
        }
        else if (Event == TEXT("ChaosIslandActivation"))
        {
            // Ativar Chaos Islands
            if (ObjectiveSystem)
            {
                ObjectiveSystem->TriggerChaosIslandEvent(0);
            }
        }
        else if (Event == TEXT("MapContractionEvent"))
        {
            // Evento de contração do mapa
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Iniciando contração do mapa"));
        }
        // Adicionar mais eventos conforme necessário
    }
}

void AAURACRONPCGPhaseManager::CalculatePhaseStartTimes()
{
    float AccumulatedTime = 0.0f;

    for (auto& PhasePair : PhaseSettings)
    {
        PhasePair.Value.PhaseStartTime = AccumulatedTime;
        AccumulatedTime += PhasePair.Value.PhaseDuration;
    }
}

// ========================================
// FUNÇÕES CONSOLIDADAS DO MAPPHASEMANAGER
// ========================================

void AAURACRONPCGPhaseManager::FindPCGActors()
{
    if (!IsValid(GetWorld()))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGPhaseManager::FindPCGActors - World inválido"));
        return;
    }

    // Usar a utility class para encontrar atores PCG
    PCGActorReferences = UAURACRONPCGUtility::FindAllPCGActors(GetWorld());

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Encontrados %d atores PCG total"),
           PCGActorReferences.GetTotalActorCount());
}

void AAURACRONPCGPhaseManager::StartMapPhases()
{
    if (!HasAuthority())
    {
        return;
    }

    // Resetar estado
    CurrentPhase = EAURACRONMapPhase::Awakening;
    NextPhase = CalculateNextPhase(CurrentPhase);
    TargetPhase = NextPhase;
    CurrentPhaseTime = 0.0f;
    bIsInTransition = false;
    TransitionProgress = 0.0f;

    // Encontrar atores PCG
    FindPCGActors();

    // Aplicar efeitos da fase inicial
    ApplyPhaseEffects(CurrentPhase, 1.0f);

    // Configurar timer para a primeira fase
    const FAURACRONPhaseSettings* PhaseConfig = PhaseSettings.Find(CurrentPhase);
    if (PhaseConfig)
    {
        GetWorld()->GetTimerManager().SetTimer(PhaseTimer, this,
            &AAURACRONPCGPhaseManager::OnPhaseTimerExpired, PhaseConfig->PhaseDuration, false);

        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Iniciando fases do mapa - Fase inicial: %s"),
               *PhaseConfig->PhaseName);
    }
}

void AAURACRONPCGPhaseManager::ForcePhaseTransition(EAURACRONMapPhase NewTargetPhase)
{
    if (!HasAuthority())
    {
        return;
    }

    // Cancelar timer atual
    GetWorld()->GetTimerManager().ClearTimer(PhaseTimer);

    // Configurar transição forçada
    NextPhase = NewTargetPhase;
    this->TargetPhase = NewTargetPhase;

    // Executar transição imediatamente
    ExecutePhaseTransition();

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Transição forçada para fase %d"), static_cast<int32>(TargetPhase));
}

void AAURACRONPCGPhaseManager::SetPhaseSystemPaused(bool bPaused)
{
    if (!HasAuthority())
    {
        return;
    }

    bIsPaused = bPaused;

    if (bPaused)
    {
        // Pausar timers
        GetWorld()->GetTimerManager().PauseTimer(PhaseTimer);
        GetWorld()->GetTimerManager().PauseTimer(PhaseProgressionTimer);
    }
    else
    {
        // Retomar timers
        GetWorld()->GetTimerManager().UnPauseTimer(PhaseTimer);
        GetWorld()->GetTimerManager().UnPauseTimer(PhaseProgressionTimer);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Sistema de fases %s"),
           bPaused ? TEXT("pausado") : TEXT("retomado"));
}

EAURACRONMapPhase AAURACRONPCGPhaseManager::CalculateNextPhase(EAURACRONMapPhase PhaseToCheck)
{
    switch (PhaseToCheck)
    {
    case EAURACRONMapPhase::Awakening:
        return EAURACRONMapPhase::Convergence;

    case EAURACRONMapPhase::Convergence:
        return EAURACRONMapPhase::Intensification;

    case EAURACRONMapPhase::Intensification:
        return EAURACRONMapPhase::Resolution;

    case EAURACRONMapPhase::Resolution:
        return EAURACRONMapPhase::Awakening; // Reiniciar ciclo

    default:
        return EAURACRONMapPhase::Awakening;
    }
}

void AAURACRONPCGPhaseManager::ExecutePhaseTransition()
{
    if (!HasAuthority() || bIsPaused)
    {
        return;
    }

    // Iniciar transição
    bIsInTransition = true;
    TransitionProgress = 0.0f;

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Iniciando transição de fase %d -> %d"),
           static_cast<int32>(CurrentPhase), static_cast<int32>(NextPhase));
}

void AAURACRONPCGPhaseManager::UpdateTransition(float DeltaTime)
{
    if (!bIsInTransition)
    {
        return;
    }

    // Atualizar progresso da transição
    TransitionProgress += DeltaTime / TransitionDuration;

    // Disparar evento de progresso
    OnPhaseTransitionProgress.Broadcast(CurrentPhase, TransitionProgress);

    // Aplicar efeitos de transição
    const FAURACRONPhaseSettings* CurrentSettings = PhaseSettings.Find(CurrentPhase);
    const FAURACRONPhaseSettings* NextSettings = PhaseSettings.Find(NextPhase);

    if (CurrentSettings && NextSettings)
    {
        // Interpolar configurações
        FAURACRONPhaseSettings BlendedSettings = InterpolatePhaseSettings(*CurrentSettings, *NextSettings, TransitionProgress);
        ApplyPhaseConfiguration(BlendedSettings, 1.0f);
    }

    // Verificar se transição terminou
    if (TransitionProgress >= 1.0f)
    {
        // Finalizar transição
        EAURACRONMapPhase OldPhase = CurrentPhase;
        CurrentPhase = NextPhase;
        NextPhase = CalculateNextPhase(CurrentPhase);
        TargetPhase = NextPhase;

        bIsInTransition = false;
        TransitionProgress = 0.0f;
        CurrentPhaseTime = 0.0f;

        // Aplicar efeitos da nova fase
        ApplyPhaseEffects(CurrentPhase, 1.0f);

        // Notificar mudança de fase
        NotifyPhaseChange(OldPhase, CurrentPhase);

        // Configurar timer para próxima fase
        const FAURACRONPhaseSettings* NewPhaseConfig = PhaseSettings.Find(CurrentPhase);
        if (NewPhaseConfig)
        {
            GetWorld()->GetTimerManager().SetTimer(PhaseTimer, this,
                &AAURACRONPCGPhaseManager::OnPhaseTimerExpired, NewPhaseConfig->PhaseDuration, false);

            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Transição concluída - Nova fase: %s"),
                   *NewPhaseConfig->PhaseName);
        }
    }
}

void AAURACRONPCGPhaseManager::ApplyPhaseEffects(EAURACRONMapPhase Phase, float Intensity)
{
    const FAURACRONPhaseSettings* Config = PhaseSettings.Find(Phase);
    if (!Config)
    {
        return;
    }

    // Aplicar efeitos usando a utility class
    UAURACRONPCGUtility::ApplyConfigurationToAllActors(PCGActorReferences, TEXT("ActivityScale"),
                                                       Config->EnvironmentActivityScale * Intensity);

    // Aplicar efeitos específicos aos ambientes
    for (AAURACRONPCGEnvironment* Environment : PCGActorReferences.EnvironmentActors)
    {
        if (IsValid(Environment))
        {
            Environment->UpdateForMapPhase(Phase);
            Environment->SetActivityScale(Config->EnvironmentActivityScale * Intensity);
        }
    }

    // Aplicar efeitos às trilhas
    for (AAURACRONPCGTrail* Trail : PCGActorReferences.TrailActors)
    {
        if (IsValid(Trail))
        {
            Trail->UpdateForMapPhase(Phase);
            Trail->SetActivityScale(Config->EnvironmentActivityScale * Intensity);
        }
    }

    // Aplicar efeitos às ilhas
    for (AAURACRONPCGIsland* Island : PCGActorReferences.IslandActors)
    {
        if (IsValid(Island))
        {
            Island->UpdateForMapPhase(Phase);
            Island->SetActivityScale(Config->EnvironmentActivityScale * Intensity);
        }
    }

    // Aplicar efeitos ao Prismal Flow
    if (IsValid(PCGActorReferences.PrismalFlowActor))
    {
        PCGActorReferences.PrismalFlowActor->UpdateForMapPhase(Phase);
        PCGActorReferences.PrismalFlowActor->SetFlowIntensity(Config->PhaseIntensity * Intensity);
    }

    // Aplicar efeitos visuais globais
    UpdateGlobalVisualEffects(Phase, Intensity);
}

void AAURACRONPCGPhaseManager::ApplyPhaseConfiguration(const FAURACRONPhaseSettings& Config, float BlendFactor)
{
    // Aplicar configuração com fator de blend para transições suaves
    float AdjustedIntensity = Config.PhaseIntensity * BlendFactor;
    float AdjustedActivityScale = Config.EnvironmentActivityScale * BlendFactor;

    // Aplicar usando utility class
    UAURACRONPCGUtility::ApplyConfigurationToAllActors(PCGActorReferences, TEXT("ActivityScale"), AdjustedActivityScale);
}

void AAURACRONPCGPhaseManager::UpdateGlobalVisualEffects(EAURACRONMapPhase Phase, float Intensity)
{
    // Implementar efeitos visuais globais baseados na fase
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Aplicar configurações de pós-processamento baseadas na fase
    APostProcessVolume* PostProcessVolume = nullptr;
    for (TActorIterator<APostProcessVolume> ActorItr(World); ActorItr; ++ActorItr)
    {
        PostProcessVolume = *ActorItr;
        if (PostProcessVolume && PostProcessVolume->bUnbound)
        {
            break;
        }
    }

    if (PostProcessVolume)
    {
        FPostProcessSettings& Settings = PostProcessVolume->Settings;
        
        switch (Phase)
        {
            case EAURACRONMapPhase::Awakening:
                // Efeitos suaves e luminosos
                Settings.ColorSaturation = FVector4(1.2f * Intensity, 1.2f * Intensity, 1.2f * Intensity, 1.0f);
                Settings.ColorGamma = FVector4(1.1f, 1.1f, 1.1f, 1.0f);
                Settings.bOverride_ColorSaturation = true;
                Settings.bOverride_ColorGamma = true;
                break;
                
            case EAURACRONMapPhase::Convergence:
                // Efeitos mais intensos e contrastados
                Settings.ColorSaturation = FVector4(1.4f * Intensity, 1.4f * Intensity, 1.4f * Intensity, 1.0f);
                Settings.ColorContrast = FVector4(1.2f, 1.2f, 1.2f, 1.0f);
                Settings.bOverride_ColorSaturation = true;
                Settings.bOverride_ColorContrast = true;
                break;
                
            case EAURACRONMapPhase::Intensification:
                // Efeitos dramáticos e saturados
                Settings.ColorSaturation = FVector4(1.6f * Intensity, 1.6f * Intensity, 1.6f * Intensity, 1.0f);
                Settings.ColorContrast = FVector4(1.4f, 1.4f, 1.4f, 1.0f);
                Settings.VignetteIntensity = 0.3f * Intensity;
                Settings.bOverride_ColorSaturation = true;
                Settings.bOverride_ColorContrast = true;
                Settings.bOverride_VignetteIntensity = true;
                break;
                
            case EAURACRONMapPhase::Resolution:
                // Efeitos finais épicos
                Settings.ColorSaturation = FVector4(1.8f * Intensity, 1.8f * Intensity, 1.8f * Intensity, 1.0f);
                Settings.ColorContrast = FVector4(1.6f, 1.6f, 1.6f, 1.0f);
                Settings.VignetteIntensity = 0.5f * Intensity;
                Settings.BloomIntensity = 1.2f * Intensity;
                Settings.bOverride_ColorSaturation = true;
                Settings.bOverride_ColorContrast = true;
                Settings.bOverride_VignetteIntensity = true;
                Settings.bOverride_BloomIntensity = true;
                break;
        }
    }

    // Atualizar iluminação global
    ADirectionalLight* DirectionalLight = nullptr;
    for (TActorIterator<ADirectionalLight> ActorItr(World); ActorItr; ++ActorItr)
    {
        DirectionalLight = *ActorItr;
        if (DirectionalLight)
        {
            UDirectionalLightComponent* LightComponent = DirectionalLight->GetComponent();
            if (LightComponent)
            {
                float BaseIntensity = 3.0f;
                LightComponent->SetIntensity(BaseIntensity * (1.0f + 0.5f * Intensity));
                
                // Ajustar cor da luz baseada na fase
                FLinearColor LightColor = FLinearColor::White;
                switch (Phase)
                {
                    case EAURACRONMapPhase::Awakening:
                        LightColor = FLinearColor(1.0f, 0.95f, 0.8f); // Luz dourada suave
                        break;
                    case EAURACRONMapPhase::Convergence:
                        LightColor = FLinearColor(0.9f, 0.9f, 1.0f); // Luz azulada
                        break;
                    case EAURACRONMapPhase::Intensification:
                        LightColor = FLinearColor(1.0f, 0.8f, 0.6f); // Luz alaranjada
                        break;
                    case EAURACRONMapPhase::Resolution:
                        LightColor = FLinearColor(1.0f, 0.7f, 0.9f); // Luz magenta épica
                        break;
                }
                LightComponent->SetLightColor(LightColor);
            }
            break;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Efeitos visuais globais atualizados para fase %d com intensidade %.2f"),
           static_cast<int32>(Phase), Intensity);
}

void AAURACRONPCGPhaseManager::NotifyPhaseChange(EAURACRONMapPhase OldPhase, EAURACRONMapPhase NewPhase)
{
    // Disparar evento de mudança de fase
    OnMapPhaseChanged.Broadcast(OldPhase, NewPhase);

    // Notificar sistemas integrados
    NotifySystemsOfPhaseChange(NewPhase);

    // Log da mudança
    const FAURACRONPhaseSettings* OldPhaseSettings = PhaseSettings.Find(OldPhase);
    const FAURACRONPhaseSettings* NewPhaseSettings = PhaseSettings.Find(NewPhase);

    const FString OldPhaseName = OldPhaseSettings ? OldPhaseSettings->PhaseName : TEXT("Unknown");
    const FString NewPhaseName = NewPhaseSettings ? NewPhaseSettings->PhaseName : TEXT("Unknown");

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Mudança de fase - %s -> %s"),
           *OldPhaseName, *NewPhaseName);
}

void AAURACRONPCGPhaseManager::OnPhaseTimerExpired()
{
    if (!HasAuthority() || bIsPaused)
    {
        return;
    }

    // Executar transição para próxima fase
    ExecutePhaseTransition();
}

bool AAURACRONPCGPhaseManager::ValidatePhaseConfiguration(const FAURACRONPhaseSettings& Config)
{
    // Validar configurações básicas
    if (Config.PhaseDuration <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGPhaseManager: Duração de fase inválida: %.2f"), Config.PhaseDuration);
        return false;
    }

    if (Config.PhaseIntensity <= 0.0f || Config.PhaseIntensity > 2.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGPhaseManager: Intensidade de fase inválida: %.2f"), Config.PhaseIntensity);
        return false;
    }

    return true;
}

void AAURACRONPCGPhaseManager::InitializePhaseConfigurations()
{
    // Compatibilidade: copiar configurações para PhaseConfigurations
    PhaseConfigurations = PhaseSettings;

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Configurações de fase inicializadas (compatibilidade)"));
}

// ========================================
// IMPLEMENTAÇÃO DA FUNÇÃO FALTANTE - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGPhaseManager::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades usando APIs modernas do UE 5.6
    DOREPLIFETIME(AAURACRONPCGPhaseManager, CurrentPhase);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, TargetPhase);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, NextPhase);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, bProgressionActive);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, bAutoStartProgression);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, PhaseSettings);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, PhaseConfigurations);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, LaneSystem);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, JungleSystem);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, ObjectiveSystem);
    DOREPLIFETIME(AAURACRONPCGPhaseManager, EnvironmentManager);
}

// ========================================
// IMPLEMENTAÇÃO DOS MÉTODOS DE EFEITOS ESPECIAIS
// ========================================

void AAURACRONPCGPhaseManager::SpawnEnergyPulses(float Intensity)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Gerando pulsos de energia com intensidade %.2f"), Intensity);
    
    // Determinar número de pulsos baseado na intensidade
    int32 NumPulses = FMath::RoundToInt(Intensity * 5.0f); // 5-10 pulsos dependendo da intensidade
    
    // Obter centro do mapa
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float MapRadius = FAURACRONMapDimensions::MAP_RADIUS_CM * 0.8f; // 80% do raio para manter dentro da área jogável
    
    // Spawnar pulsos de energia em posições aleatórias
    for (int32 i = 0; i < NumPulses; ++i)
    {
        // Gerar posição aleatória dentro do mapa
        float Angle = FMath::RandRange(0.0f, 2.0f * PI);
        float Distance = FMath::RandRange(0.0f, MapRadius);
        FVector SpawnLocation = MapCenter + FVector(FMath::Cos(Angle) * Distance, FMath::Sin(Angle) * Distance, 100.0f);
        
        // Configurar rotação aleatória
        FRotator SpawnRotation = FRotator(0.0f, FMath::RandRange(0.0f, 360.0f), 0.0f);
        
        // Spawnar o ator de pulso de energia
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        AAURACRONPCGEnergyPulse* EnergyPulse = GetWorld()->SpawnActor<AAURACRONPCGEnergyPulse>(
            AAURACRONPCGEnergyPulse::StaticClass(), 
            SpawnLocation, 
            SpawnRotation, 
            SpawnParams);
        
        if (EnergyPulse)
        {
            // Configurar propriedades do pulso
            EnergyPulse->SetPulseIntensity(Intensity);
            EnergyPulse->SetPulseRadius(FMath::RandRange(500.0f, 1500.0f));
            EnergyPulse->SetPulseDuration(FMath::RandRange(10.0f, 30.0f));
            
            UE_LOG(LogTemp, Verbose, TEXT("AURACRONPCGPhaseManager: Pulso de energia gerado em %s"), 
                   *SpawnLocation.ToString());
        }
    }
}

void AAURACRONPCGPhaseManager::SpawnChaosPortals(float Intensity)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGPhaseManager: Gerando portais de caos com intensidade %.2f"), Intensity);
    
    // Determinar número de portais baseado na intensidade
    int32 NumPortals = FMath::RoundToInt(Intensity * 3.0f); // 3-6 portais dependendo da intensidade
    
    // Obter centro do mapa
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float MapRadius = FAURACRONMapDimensions::MAP_RADIUS_CM * 0.7f; // 70% do raio para manter dentro da área jogável
    
    // Spawnar portais em posições estratégicas
    for (int32 i = 0; i < NumPortals; ++i)
    {
        // Gerar posição aleatória dentro do mapa
        float Angle = FMath::RandRange(0.0f, 2.0f * PI);
        float Distance = FMath::RandRange(MapRadius * 0.3f, MapRadius); // Não muito perto do centro
        FVector SpawnLocation = MapCenter + FVector(FMath::Cos(Angle) * Distance, FMath::Sin(Angle) * Distance, 50.0f);
        
        // Configurar rotação aleatória
        FRotator SpawnRotation = FRotator(0.0f, FMath::RandRange(0.0f, 360.0f), 0.0f);
        
        // Spawnar o ator de portal de caos
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        AAURACRONPCGChaosPortal* ChaosPortal = GetWorld()->SpawnActor<AAURACRONPCGChaosPortal>(
            AAURACRONPCGChaosPortal::StaticClass(), 
            SpawnLocation, 
            SpawnRotation, 
            SpawnParams);
        
        if (ChaosPortal)
        {
            // Configurar propriedades do portal
            ChaosPortal->SetPortalIntensity(Intensity);
            ChaosPortal->SetPortalRadius(FMath::RandRange(300.0f, 800.0f));
            ChaosPortal->SetPortalLifetime(FMath::RandRange(60.0f, 180.0f)); // 1-3 minutos
            
            // Configurar tipo de portal baseado na intensidade
            EChaosPortalType PortalType = (FMath::RandRange(0.0f, 1.0f) > 0.7f) ? 
                EChaosPortalType::Elite : EChaosPortalType::Standard;
                
            ChaosPortal->SetPortalType(PortalType);
            
            UE_LOG(LogTemp, Verbose, TEXT("AURACRONPCGPhaseManager: Portal de caos tipo %d gerado em %s"), 
                   static_cast<int32>(PortalType), *SpawnLocation.ToString());
        }
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES ADICIONAIS - UE 5.6 MODERN APIS
// ========================================

bool AAURACRONPCGPhaseManager::IsEnvironmentManagerRegistered() const
{
    return RegisteredEnvironmentManager != nullptr && IsValid(RegisteredEnvironmentManager);
}

void AAURACRONPCGPhaseManager::RegisterEnvironmentManager(AAURACRONPCGEnvironmentManager* InEnvironmentManager)
{
    if (!InEnvironmentManager || !IsValid(InEnvironmentManager))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::RegisterEnvironmentManager - Invalid EnvironmentManager"));
        return;
    }

    // Desregistrar o anterior se existir
    if (RegisteredEnvironmentManager && IsValid(RegisteredEnvironmentManager))
    {
        UnregisterEnvironmentManager();
    }

    RegisteredEnvironmentManager = InEnvironmentManager;

    // Conectar delegate para notificações de mudança de fase
    if (!OnPhaseChanged.IsBound())
    {
        OnPhaseChanged.AddDynamic(RegisteredEnvironmentManager, &AAURACRONPCGEnvironmentManager::OnMapPhaseChanged);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::RegisterEnvironmentManager - Environment Manager registered successfully"));
}

void AAURACRONPCGPhaseManager::UnregisterEnvironmentManager()
{
    if (RegisteredEnvironmentManager && IsValid(RegisteredEnvironmentManager))
    {
        // Desconectar delegate
        OnPhaseChanged.RemoveDynamic(RegisteredEnvironmentManager, &AAURACRONPCGEnvironmentManager::OnMapPhaseChanged);

        RegisteredEnvironmentManager = nullptr;

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::UnregisterEnvironmentManager - Environment Manager unregistered"));
    }
}

bool AAURACRONPCGPhaseManager::ValidateEnvironmentConfiguration(EAURACRONMapPhase Phase)
{
    // Implementação robusta de validação usando APIs modernas do UE 5.6
    if (!RegisteredEnvironmentManager || !IsValid(RegisteredEnvironmentManager))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::ValidateEnvironmentConfiguration - No Environment Manager registered"));
        return false;
    }

    // Validar configurações específicas para cada fase
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            // Validar se todos os ambientes básicos estão configurados
            if (RegisteredEnvironmentManager->GetAllEnvironmentInstances().Num() < 3)
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::ValidateEnvironmentConfiguration - Insufficient environments for Awakening phase"));
                return false;
            }
            break;

        case EAURACRONMapPhase::Convergence:
            // Validar se os sistemas de convergência estão prontos
            if (!RegisteredEnvironmentManager->AreConvergenceSystemsReady())
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::ValidateEnvironmentConfiguration - Convergence systems not ready"));
                return false;
            }
            break;

        case EAURACRONMapPhase::Intensification:
            // Validar se os sistemas de intensificação estão configurados
            if (!RegisteredEnvironmentManager->AreIntensificationSystemsReady())
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::ValidateEnvironmentConfiguration - Intensification systems not ready"));
                return false;
            }
            break;

        case EAURACRONMapPhase::Resolution:
            // Validar se os sistemas de resolução estão prontos
            if (!RegisteredEnvironmentManager->AreResolutionSystemsReady())
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::ValidateEnvironmentConfiguration - Resolution systems not ready"));
                return false;
            }
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::ValidateEnvironmentConfiguration - Configuration valid for phase %d"), (int32)Phase);
    return true;
}

void AAURACRONPCGPhaseManager::ApplyEnvironmentConfiguration(EAURACRONMapPhase Phase)
{
    // Implementação robusta de aplicação de configuração usando APIs modernas do UE 5.6
    if (!RegisteredEnvironmentManager || !IsValid(RegisteredEnvironmentManager))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::ApplyEnvironmentConfiguration - No Environment Manager registered"));
        return;
    }

    // Aplicar configurações específicas para cada fase
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            // Configurar ambientes para fase de despertar
            RegisteredEnvironmentManager->ConfigureForAwakeningPhaseModern();
            break;

        case EAURACRONMapPhase::Convergence:
            // Configurar ambientes para fase de convergência
            RegisteredEnvironmentManager->ConfigureForConvergencePhaseModern();
            break;

        case EAURACRONMapPhase::Intensification:
            // Configurar ambientes para fase de intensificação
            RegisteredEnvironmentManager->ConfigureForIntensificationPhaseModern();
            break;

        case EAURACRONMapPhase::Resolution:
            // Configurar ambientes para fase de resolução
            RegisteredEnvironmentManager->ConfigureForResolutionPhaseModern();
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::ApplyEnvironmentConfiguration - Configuration applied for phase %d"), (int32)Phase);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGPhaseManager::ConfigureForEntryDevice(bool bIsEntryDevice)
{
    // Implementação robusta para configurar para dispositivo de entrada
    if (bIsEntryDevice)
    {
        // Configurar para dispositivos de entrada (mobile, low-end)
        ProgressionSpeedMultiplier = 0.8f; // Progressão mais lenta

        // Configurar environment manager para dispositivos de entrada
        if (EnvironmentManager && IsValid(EnvironmentManager))
        {
            EnvironmentManager->ConfigureForAwakeningPhaseModern();
        }

        // Configurar sistemas para menor performance (usando funções existentes)
        if (LaneSystem && IsValid(LaneSystem))
        {
            LaneSystem->UpdateForMapPhase(EAURACRONMapPhase::Awakening);
        }

        if (JungleSystem && IsValid(JungleSystem))
        {
            JungleSystem->SetAdaptiveDifficulty(0.7f);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::ConfigureForEntryDevice - Configured for entry device"));
    }
    else
    {
        // Configurar para dispositivos de alta performance
        ProgressionSpeedMultiplier = 1.0f;

        // Configurar environment manager para alta performance
        if (EnvironmentManager && IsValid(EnvironmentManager))
        {
            EnvironmentManager->ConfigureForConvergencePhaseModern();
        }

        // Configurar sistemas para alta performance (usando funções existentes)
        if (LaneSystem && IsValid(LaneSystem))
        {
            LaneSystem->UpdateForMapPhase(EAURACRONMapPhase::Convergence);
        }

        if (JungleSystem && IsValid(JungleSystem))
        {
            JungleSystem->SetAdaptiveDifficulty(1.0f);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::ConfigureForEntryDevice - Configured for high-end device"));
    }
}

void AAURACRONPCGPhaseManager::ApplyAwakeningPhaseSettings()
{
    // Implementação robusta para aplicar configurações da fase Awakening
    CurrentPhase = EAURACRONMapPhase::Awakening;

    // Configurar duração da fase
    TimeRemainingInPhase = 300.0f; // 5 minutos

    // Configurar sistemas para fase Awakening (usando funções existentes)
    if (LaneSystem && IsValid(LaneSystem))
    {
        LaneSystem->UpdateForMapPhase(EAURACRONMapPhase::Awakening);
    }

    if (JungleSystem && IsValid(JungleSystem))
    {
        JungleSystem->SetAdaptiveDifficulty(0.8f);
    }

    if (ObjectiveSystem && IsValid(ObjectiveSystem))
    {
        ObjectiveSystem->UpdateForMapPhase(EAURACRONMapPhase::Awakening);
    }

    if (EnvironmentManager && IsValid(EnvironmentManager))
    {
        EnvironmentManager->ConfigureForAwakeningPhaseModern();
    }

    // Configurar todas as ilhas para fase Awakening
    UWorld* World = GetWorld();
    if (World)
    {
        for (TActorIterator<AAURACRONPCGIsland> ActorItr(World); ActorItr; ++ActorItr)
        {
            AAURACRONPCGIsland* Island = *ActorItr;
            if (Island && IsValid(Island))
            {
                Island->ConfigureForAwakeningPhase(true);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::ApplyAwakeningPhaseSettings - Awakening phase settings applied"));
}

void AAURACRONPCGPhaseManager::IntegrateSystemsForAwakeningPhase(bool bEnable)
{
    // Implementação robusta para integrar sistemas para fase Awakening
    if (bEnable)
    {
        // Integrar todos os sistemas para trabalhar em conjunto na fase Awakening
        if (LaneSystem && JungleSystem && IsValid(LaneSystem) && IsValid(JungleSystem))
        {
            // Configurar sistemas para trabalhar em conjunto (usando funções existentes)
            LaneSystem->UpdateForMapPhase(EAURACRONMapPhase::Awakening);
            JungleSystem->SetAdaptiveDifficulty(0.8f);
        }

        if (ObjectiveSystem && EnvironmentManager && IsValid(ObjectiveSystem) && IsValid(EnvironmentManager))
        {
            // Configurar objective system e environment manager
            ObjectiveSystem->UpdateForMapPhase(EAURACRONMapPhase::Awakening);
            EnvironmentManager->ConfigureForAwakeningPhaseModern();
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::IntegrateSystemsForAwakeningPhase - Systems integrated for Awakening phase"));
    }
    else
    {
        // Desintegrar sistemas (usando funções existentes)
        if (LaneSystem && IsValid(LaneSystem))
        {
            LaneSystem->UpdateForMapPhase(EAURACRONMapPhase::Awakening);
        }

        if (JungleSystem && IsValid(JungleSystem))
        {
            JungleSystem->SetAdaptiveDifficulty(1.0f);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::IntegrateSystemsForAwakeningPhase - Systems disintegrated"));
    }
}

void AAURACRONPCGPhaseManager::ApplyConvergencePhaseSettings()
{
    // Implementação robusta para aplicar configurações da fase Convergence
    CurrentPhase = EAURACRONMapPhase::Convergence;

    // Configurar duração da fase
    TimeRemainingInPhase = 600.0f; // 10 minutos

    // Configurar sistemas para fase Convergence (usando funções existentes)
    if (LaneSystem && IsValid(LaneSystem))
    {
        LaneSystem->UpdateForMapPhase(EAURACRONMapPhase::Convergence);
    }

    if (JungleSystem && IsValid(JungleSystem))
    {
        JungleSystem->SetAdaptiveDifficulty(1.2f);
    }

    if (ObjectiveSystem && IsValid(ObjectiveSystem))
    {
        ObjectiveSystem->UpdateForMapPhase(EAURACRONMapPhase::Convergence);
    }

    if (EnvironmentManager && IsValid(EnvironmentManager))
    {
        EnvironmentManager->ConfigureForConvergencePhaseModern();
    }

    // Configurar todas as ilhas para fase Convergence
    UWorld* World = GetWorld();
    if (World)
    {
        for (TActorIterator<AAURACRONPCGIsland> ActorItr(World); ActorItr; ++ActorItr)
        {
            AAURACRONPCGIsland* Island = *ActorItr;
            if (Island && IsValid(Island))
            {
                Island->ConfigureForConvergencePhase(true, false, false);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::ApplyConvergencePhaseSettings - Convergence phase settings applied"));
}

void AAURACRONPCGPhaseManager::IntegrateSystemsForConvergencePhase(bool bEnable)
{
    // Implementação robusta para integrar sistemas para fase Convergence
    if (bEnable)
    {
        // Integrar sistemas com maior intensidade para Convergence
        if (LaneSystem && JungleSystem && IsValid(LaneSystem) && IsValid(JungleSystem))
        {
            // Configurar sistemas para fase Convergence (usando funções existentes)
            LaneSystem->UpdateForMapPhase(EAURACRONMapPhase::Convergence);
            JungleSystem->SetAdaptiveDifficulty(1.2f);
        }

        if (ObjectiveSystem && EnvironmentManager && IsValid(ObjectiveSystem) && IsValid(EnvironmentManager))
        {
            // Configurar objective system e environment manager
            ObjectiveSystem->UpdateForMapPhase(EAURACRONMapPhase::Convergence);
            EnvironmentManager->ConfigureForConvergencePhaseModern();
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::IntegrateSystemsForConvergencePhase - Systems integrated for Convergence phase"));
    }
    else
    {
        // Reduzir integração (usando funções existentes)
        if (LaneSystem && IsValid(LaneSystem))
        {
            LaneSystem->UpdateForMapPhase(EAURACRONMapPhase::Awakening);
        }

        if (JungleSystem && IsValid(JungleSystem))
        {
            JungleSystem->SetAdaptiveDifficulty(1.0f);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::IntegrateSystemsForConvergencePhase - Systems integration reduced"));
    }
}

void AAURACRONPCGPhaseManager::ConfigureEnvironmentsForDeviceType(bool bIsLowEndDevice)
{
    // Implementação robusta para configurar ambientes baseado no tipo de dispositivo
    if (bIsLowEndDevice)
    {
        // Configurar para dispositivos de baixa performance (usando funções existentes)
        if (EnvironmentManager && IsValid(EnvironmentManager))
        {
            EnvironmentManager->ConfigureForAwakeningPhaseModern();
        }

        // Reduzir qualidade visual em todos os sistemas
        UWorld* World = GetWorld();
        if (World)
        {
            for (TActorIterator<AAURACRONPCGIsland> ActorItr(World); ActorItr; ++ActorItr)
            {
                AAURACRONPCGIsland* Island = *ActorItr;
                if (Island && IsValid(Island))
                {
                    Island->SetActivityLevel(0.4f);
                }
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::ConfigureEnvironmentsForDeviceType - Configured for low-end device"));
    }
    else
    {
        // Configurar para dispositivos de alta performance (usando funções existentes)
        if (EnvironmentManager && IsValid(EnvironmentManager))
        {
            EnvironmentManager->ConfigureForIntensificationPhaseModern();
        }

        // Máxima qualidade visual
        UWorld* World = GetWorld();
        if (World)
        {
            for (TActorIterator<AAURACRONPCGIsland> ActorItr(World); ActorItr; ++ActorItr)
            {
                AAURACRONPCGIsland* Island = *ActorItr;
                if (Island && IsValid(Island))
                {
                    Island->SetActivityLevel(1.0f);
                }
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::ConfigureEnvironmentsForDeviceType - Configured for high-end device"));
    }
}

bool AAURACRONPCGPhaseManager::VerifyDocumentationCompliance(EAURACRONMapPhase Phase, bool bIsEntryDevice)
{
    // Implementação robusta para verificar conformidade com documentação
    bool bIsCompliant = true;

    // Verificar se a fase está configurada corretamente
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            // Verificar configurações da fase Awakening
            if (!LaneSystem || !IsValid(LaneSystem))
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::VerifyDocumentationCompliance - LaneSystem not configured for Awakening"));
                bIsCompliant = false;
            }

            if (!JungleSystem || !IsValid(JungleSystem))
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::VerifyDocumentationCompliance - JungleSystem not configured for Awakening"));
                bIsCompliant = false;
            }
            break;

        case EAURACRONMapPhase::Convergence:
            // Verificar configurações da fase Convergence
            if (!ObjectiveSystem || !IsValid(ObjectiveSystem))
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::VerifyDocumentationCompliance - ObjectiveSystem not configured for Convergence"));
                bIsCompliant = false;
            }
            break;

        case EAURACRONMapPhase::Intensification:
            // Verificar configurações da fase Intensification
            if (!EnvironmentManager || !IsValid(EnvironmentManager))
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::VerifyDocumentationCompliance - EnvironmentManager not configured for Intensification"));
                bIsCompliant = false;
            }
            break;

        case EAURACRONMapPhase::Resolution:
            // Verificar configurações da fase Resolution
            if (TimeRemainingInPhase <= 0.0f)
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::VerifyDocumentationCompliance - Invalid time remaining for Resolution phase"));
                bIsCompliant = false;
            }
            break;
    }

    // Verificar configurações específicas do dispositivo
    if (bIsEntryDevice)
    {
        if (ProgressionSpeedMultiplier > 1.0f)
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPhaseManager::VerifyDocumentationCompliance - Progression speed too high for entry device"));
            bIsCompliant = false;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::VerifyDocumentationCompliance - Compliance check %s for phase %d"),
           bIsCompliant ? TEXT("PASSED") : TEXT("FAILED"), (int32)Phase);

    return bIsCompliant;
}

void AAURACRONPCGPhaseManager::FixDocumentationComplianceIssues(EAURACRONMapPhase Phase, bool bIsEntryDevice)
{
    // Implementação robusta para corrigir problemas de conformidade
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::FixDocumentationComplianceIssues - Fixing compliance issues for phase %d"), (int32)Phase);

    // Corrigir problemas baseados na fase
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            // Corrigir configurações da fase Awakening
            if (!LaneSystem || !IsValid(LaneSystem))
            {
                // Tentar encontrar LaneSystem no mundo
                UWorld* World = GetWorld();
                if (World)
                {
                    for (TActorIterator<AAURACRONPCGLaneSystem> ActorItr(World); ActorItr; ++ActorItr)
                    {
                        LaneSystem = *ActorItr;
                        break;
                    }
                }
            }

            if (!JungleSystem || !IsValid(JungleSystem))
            {
                // Tentar encontrar JungleSystem no mundo
                UWorld* World = GetWorld();
                if (World)
                {
                    for (TActorIterator<AAURACRONPCGJungleSystem> ActorItr(World); ActorItr; ++ActorItr)
                    {
                        JungleSystem = *ActorItr;
                        break;
                    }
                }
            }
            break;

        case EAURACRONMapPhase::Convergence:
            // Corrigir configurações da fase Convergence
            if (!ObjectiveSystem || !IsValid(ObjectiveSystem))
            {
                // Tentar encontrar ObjectiveSystem no mundo
                UWorld* World = GetWorld();
                if (World)
                {
                    for (TActorIterator<AAURACRONPCGObjectiveSystem> ActorItr(World); ActorItr; ++ActorItr)
                    {
                        ObjectiveSystem = *ActorItr;
                        break;
                    }
                }
            }
            break;

        case EAURACRONMapPhase::Intensification:
            // Corrigir configurações da fase Intensification
            if (!EnvironmentManager || !IsValid(EnvironmentManager))
            {
                // Tentar encontrar EnvironmentManager no mundo
                UWorld* World = GetWorld();
                if (World)
                {
                    for (TActorIterator<AAURACRONPCGEnvironmentManager> ActorItr(World); ActorItr; ++ActorItr)
                    {
                        EnvironmentManager = *ActorItr;
                        break;
                    }
                }
            }
            break;

        case EAURACRONMapPhase::Resolution:
            // Corrigir configurações da fase Resolution
            if (TimeRemainingInPhase <= 0.0f)
            {
                TimeRemainingInPhase = 300.0f; // 5 minutos padrão
            }
            break;
    }

    // Corrigir configurações específicas do dispositivo
    if (bIsEntryDevice && ProgressionSpeedMultiplier > 1.0f)
    {
        ProgressionSpeedMultiplier = 0.8f; // Reduzir para dispositivos de entrada
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPhaseManager::FixDocumentationComplianceIssues - Compliance issues fixed for phase %d"), (int32)Phase);
}
