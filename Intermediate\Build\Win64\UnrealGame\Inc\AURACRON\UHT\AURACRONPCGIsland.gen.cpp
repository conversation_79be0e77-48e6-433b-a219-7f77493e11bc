// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGIsland.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGIsland() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGIsland();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGIsland_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONIslandType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSettings_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAURACRONPCGIsland Function ConfigureForAwakeningPhase *******************
struct Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics
{
	struct AURACRONPCGIsland_eventConfigureForAwakeningPhase_Parms
	{
		bool bIsEntryDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar ilha para Fase 1: Despertar\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar ilha para Fase 1: Despertar" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGIsland_eventConfigureForAwakeningPhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGIsland_eventConfigureForAwakeningPhase_Parms), &Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::NewProp_bIsEntryDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "ConfigureForAwakeningPhase", Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::AURACRONPCGIsland_eventConfigureForAwakeningPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::AURACRONPCGIsland_eventConfigureForAwakeningPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execConfigureForAwakeningPhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForAwakeningPhase(Z_Param_bIsEntryDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function ConfigureForAwakeningPhase *********************

// ********** Begin Class AAURACRONPCGIsland Function ConfigureForConvergencePhase *****************
struct Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics
{
	struct AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms
	{
		bool bIsEntryDevice;
		bool bIsMidDevice;
		bool bIsHighDevice;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar ilha para Fase 2: Converg\xc3\xaancia\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar ilha para Fase 2: Converg\xc3\xaancia" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsEntryDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntryDevice;
	static void NewProp_bIsMidDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMidDevice;
	static void NewProp_bIsHighDevice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsHighDevice;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit(void* Obj)
{
	((AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms*)Obj)->bIsEntryDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice = { "bIsEntryDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice_SetBit(void* Obj)
{
	((AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms*)Obj)->bIsMidDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice = { "bIsMidDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice_SetBit(void* Obj)
{
	((AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms*)Obj)->bIsHighDevice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice = { "bIsHighDevice", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms), &Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsEntryDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsMidDevice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::NewProp_bIsHighDevice,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "ConfigureForConvergencePhase", Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::AURACRONPCGIsland_eventConfigureForConvergencePhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execConfigureForConvergencePhase)
{
	P_GET_UBOOL(Z_Param_bIsEntryDevice);
	P_GET_UBOOL(Z_Param_bIsMidDevice);
	P_GET_UBOOL(Z_Param_bIsHighDevice);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureForConvergencePhase(Z_Param_bIsEntryDevice,Z_Param_bIsMidDevice,Z_Param_bIsHighDevice);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function ConfigureForConvergencePhase *******************

// ********** Begin Class AAURACRONPCGIsland Function GenerateIsland *******************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_GenerateIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gerar a ilha procedural\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar a ilha procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_GenerateIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "GenerateIsland", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GenerateIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_GenerateIsland_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_GenerateIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_GenerateIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execGenerateIsland)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateIsland();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function GenerateIsland *********************************

// ********** Begin Class AAURACRONPCGIsland Function GetIslandSize ********************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics
{
	struct AURACRONPCGIsland_eventGetIslandSize_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter o tamanho da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter o tamanho da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventGetIslandSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "GetIslandSize", Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::AURACRONPCGIsland_eventGetIslandSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::AURACRONPCGIsland_eventGetIslandSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execGetIslandSize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetIslandSize();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function GetIslandSize **********************************

// ********** Begin Class AAURACRONPCGIsland Function GetIslandType ********************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics
{
	struct AURACRONPCGIsland_eventGetIslandType_Parms
	{
		EAURACRONIslandType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter o tipo de ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter o tipo de ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventGetIslandType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONIslandType, METADATA_PARAMS(0, nullptr) }; // 3618671819
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "GetIslandType", Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::AURACRONPCGIsland_eventGetIslandType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::AURACRONPCGIsland_eventGetIslandType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execGetIslandType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONIslandType*)Z_Param__Result=P_THIS->GetIslandType();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function GetIslandType **********************************

// ********** Begin Class AAURACRONPCGIsland Function GetPCGComponent ******************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics
{
	struct AURACRONPCGIsland_eventGetPCGComponent_Parms
	{
		UPCGComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter componente PCG para acesso externo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter componente PCG para acesso externo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventGetPCGComponent_Parms, ReturnValue), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "GetPCGComponent", Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::AURACRONPCGIsland_eventGetPCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::AURACRONPCGIsland_eventGetPCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execGetPCGComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGComponent**)Z_Param__Result=P_THIS->GetPCGComponent();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function GetPCGComponent ********************************

// ********** Begin Class AAURACRONPCGIsland Function SetActivityScale *****************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics
{
	struct AURACRONPCGIsland_eventSetActivityScale_Parms
	{
		float Scale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a escala de atividade da ilha (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a escala de atividade da ilha (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventSetActivityScale_Parms, Scale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::NewProp_Scale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "SetActivityScale", Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::AURACRONPCGIsland_eventSetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::AURACRONPCGIsland_eventSetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execSetActivityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Scale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityScale(Z_Param_Scale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function SetActivityScale *******************************

// ********** Begin Class AAURACRONPCGIsland Function SetFullyEmerged ******************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics
{
	struct AURACRONPCGIsland_eventSetFullyEmerged_Parms
	{
		bool bFullyEmerged;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir se a ilha est\xc3\xa1 totalmente emergida\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir se a ilha est\xc3\xa1 totalmente emergida" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bFullyEmerged_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFullyEmerged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::NewProp_bFullyEmerged_SetBit(void* Obj)
{
	((AURACRONPCGIsland_eventSetFullyEmerged_Parms*)Obj)->bFullyEmerged = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::NewProp_bFullyEmerged = { "bFullyEmerged", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGIsland_eventSetFullyEmerged_Parms), &Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::NewProp_bFullyEmerged_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::NewProp_bFullyEmerged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "SetFullyEmerged", Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::AURACRONPCGIsland_eventSetFullyEmerged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::AURACRONPCGIsland_eventSetFullyEmerged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execSetFullyEmerged)
{
	P_GET_UBOOL(Z_Param_bFullyEmerged);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFullyEmerged(Z_Param_bFullyEmerged);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function SetFullyEmerged ********************************

// ********** Begin Class AAURACRONPCGIsland Function SetIslandHeight ******************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics
{
	struct AURACRONPCGIsland_eventSetIslandHeight_Parms
	{
		float Height;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a altura da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a altura da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventSetIslandHeight_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::NewProp_Height,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "SetIslandHeight", Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::AURACRONPCGIsland_eventSetIslandHeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::AURACRONPCGIsland_eventSetIslandHeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execSetIslandHeight)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Height);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetIslandHeight(Z_Param_Height);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function SetIslandHeight ********************************

// ********** Begin Class AAURACRONPCGIsland Function SetIslandPosition ****************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics
{
	struct AURACRONPCGIsland_eventSetIslandPosition_Parms
	{
		FVector Position;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a posi\xc3\xa7\xc3\xa3o da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a posi\xc3\xa7\xc3\xa3o da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventSetIslandPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::NewProp_Position,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "SetIslandPosition", Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::AURACRONPCGIsland_eventSetIslandPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::AURACRONPCGIsland_eventSetIslandPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execSetIslandPosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetIslandPosition(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function SetIslandPosition ******************************

// ********** Begin Class AAURACRONPCGIsland Function SetIslandSize ********************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics
{
	struct AURACRONPCGIsland_eventSetIslandSize_Parms
	{
		float Size;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir o tamanho da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir o tamanho da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Size;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::NewProp_Size = { "Size", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventSetIslandSize_Parms, Size), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::NewProp_Size,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "SetIslandSize", Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::AURACRONPCGIsland_eventSetIslandSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::AURACRONPCGIsland_eventSetIslandSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execSetIslandSize)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Size);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetIslandSize(Z_Param_Size);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function SetIslandSize **********************************

// ********** Begin Class AAURACRONPCGIsland Function SetIslandType ********************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics
{
	struct AURACRONPCGIsland_eventSetIslandType_Parms
	{
		EAURACRONIslandType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar o tipo de ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar o tipo de ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventSetIslandType_Parms, NewType), Z_Construct_UEnum_AURACRON_EAURACRONIslandType, METADATA_PARAMS(0, nullptr) }; // 3618671819
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "SetIslandType", Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::AURACRONPCGIsland_eventSetIslandType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::AURACRONPCGIsland_eventSetIslandType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execSetIslandType)
{
	P_GET_ENUM(EAURACRONIslandType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetIslandType(EAURACRONIslandType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function SetIslandType **********************************

// ********** Begin Class AAURACRONPCGIsland Function SetIslandVisibility **************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics
{
	struct AURACRONPCGIsland_eventSetIslandVisibility_Parms
	{
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a visibilidade da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a visibilidade da ilha" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AURACRONPCGIsland_eventSetIslandVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGIsland_eventSetIslandVisibility_Parms), &Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "SetIslandVisibility", Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::AURACRONPCGIsland_eventSetIslandVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::AURACRONPCGIsland_eventSetIslandVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execSetIslandVisibility)
{
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetIslandVisibility(Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function SetIslandVisibility ****************************

// ********** Begin Class AAURACRONPCGIsland Function UpdateForMapPhase ****************************
struct Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics
{
	struct AURACRONPCGIsland_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualizar a ilha com base na fase do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar a ilha com base na fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGIsland_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGIsland, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::AURACRONPCGIsland_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::AURACRONPCGIsland_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGIsland::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGIsland Function UpdateForMapPhase ******************************

// ********** Begin Class AAURACRONPCGIsland *******************************************************
void AAURACRONPCGIsland::StaticRegisterNativesAAURACRONPCGIsland()
{
	UClass* Class = AAURACRONPCGIsland::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ConfigureForAwakeningPhase", &AAURACRONPCGIsland::execConfigureForAwakeningPhase },
		{ "ConfigureForConvergencePhase", &AAURACRONPCGIsland::execConfigureForConvergencePhase },
		{ "GenerateIsland", &AAURACRONPCGIsland::execGenerateIsland },
		{ "GetIslandSize", &AAURACRONPCGIsland::execGetIslandSize },
		{ "GetIslandType", &AAURACRONPCGIsland::execGetIslandType },
		{ "GetPCGComponent", &AAURACRONPCGIsland::execGetPCGComponent },
		{ "SetActivityScale", &AAURACRONPCGIsland::execSetActivityScale },
		{ "SetFullyEmerged", &AAURACRONPCGIsland::execSetFullyEmerged },
		{ "SetIslandHeight", &AAURACRONPCGIsland::execSetIslandHeight },
		{ "SetIslandPosition", &AAURACRONPCGIsland::execSetIslandPosition },
		{ "SetIslandSize", &AAURACRONPCGIsland::execSetIslandSize },
		{ "SetIslandType", &AAURACRONPCGIsland::execSetIslandType },
		{ "SetIslandVisibility", &AAURACRONPCGIsland::execSetIslandVisibility },
		{ "UpdateForMapPhase", &AAURACRONPCGIsland::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGIsland;
UClass* AAURACRONPCGIsland::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGIsland;
	if (!Z_Registration_Info_UClass_AAURACRONPCGIsland.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGIsland"),
			Z_Registration_Info_UClass_AAURACRONPCGIsland.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGIsland,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGIsland.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGIsland_NoRegister()
{
	return AAURACRONPCGIsland::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator para gerenciar uma ilha flutuante espec\xc3\xad""fica no AURACRON\n * Cada tipo de ilha (Sanctuary, Nexus, Battlefield) ter\xc3\xa1 sua pr\xc3\xb3pria inst\xc3\xa2ncia\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGIsland.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator para gerenciar uma ilha flutuante espec\xc3\xad""fica no AURACRON\nCada tipo de ilha (Sanctuary, Nexus, Battlefield) ter\xc3\xa1 sua pr\xc3\xb3pria inst\xc3\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente PCG principal para gera\xc3\xa7\xc3\xa3o da ilha\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG principal para gera\xc3\xa7\xc3\xa3o da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionComponent_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente de colis\xc3\xa3o da ilha\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandMesh_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh principal da ilha\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh principal da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandSettings_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es PCG para esta ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es PCG para esta ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasHealingFountain_MetaData[] = {
		{ "Category", "AURACRON|PCG|Sanctuary" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Caracter\xc3\xadsticas espec\xc3\xad""ficas da ilha\n// Sanctuary\n" },
#endif
		{ "EditCondition", "IslandType == EAURACRONIslandType::Sanctuary" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Caracter\xc3\xadsticas espec\xc3\xad""ficas da ilha\nSanctuary" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasProtectiveBarrier_MetaData[] = {
		{ "Category", "AURACRON|PCG|Sanctuary" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Sanctuary" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasAncientTree_MetaData[] = {
		{ "Category", "AURACRON|PCG|Sanctuary" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Sanctuary" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasResourceNodes_MetaData[] = {
		{ "Category", "AURACRON|PCG|Sanctuary" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Sanctuary" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasCentralSpire_MetaData[] = {
		{ "Category", "AURACRON|PCG|Nexus" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Nexus\n" },
#endif
		{ "EditCondition", "IslandType == EAURACRONIslandType::Nexus" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nexus" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasEnergyVortex_MetaData[] = {
		{ "Category", "AURACRON|PCG|Nexus" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Nexus" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasAncientRunes_MetaData[] = {
		{ "Category", "AURACRON|PCG|Nexus" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Nexus" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasPortalGateways_MetaData[] = {
		{ "Category", "AURACRON|PCG|Nexus" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Nexus" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasStrategicPoints_MetaData[] = {
		{ "Category", "AURACRON|PCG|Battlefield" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Battlefield\n" },
#endif
		{ "EditCondition", "IslandType == EAURACRONIslandType::Battlefield" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Battlefield" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasDestructibleElements_MetaData[] = {
		{ "Category", "AURACRON|PCG|Battlefield" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Battlefield" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasElevationChanges_MetaData[] = {
		{ "Category", "AURACRON|PCG|Battlefield" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Battlefield" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasHazardZones_MetaData[] = {
		{ "Category", "AURACRON|PCG|Battlefield" },
		{ "EditCondition", "IslandType == EAURACRONIslandType::Battlefield" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandType_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tipo de ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityScale_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fase atual do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo acumulado para anima\xc3\xa7\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado para anima\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes gerados dinamicamente\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes gerados dinamicamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandSize_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tamanho da ilha em unidades do Unreal (1 unidade = 1cm)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho da ilha em unidades do Unreal (1 unidade = 1cm)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandHeight_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Altura da ilha em unidades do Unreal (1 unidade = 1cm)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura da ilha em unidades do Unreal (1 unidade = 1cm)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeSinceLastUpdate_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo decorrido desde a \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo decorrido desde a \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateInterval_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intervalo de atualiza\xc3\xa7\xc3\xa3o da ilha em segundos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de atualiza\xc3\xa7\xc3\xa3o da ilha em segundos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollisionComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandSettings;
	static void NewProp_bHasHealingFountain_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasHealingFountain;
	static void NewProp_bHasProtectiveBarrier_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasProtectiveBarrier;
	static void NewProp_bHasAncientTree_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasAncientTree;
	static void NewProp_bHasResourceNodes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasResourceNodes;
	static void NewProp_bHasCentralSpire_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasCentralSpire;
	static void NewProp_bHasEnergyVortex_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasEnergyVortex;
	static void NewProp_bHasAncientRunes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasAncientRunes;
	static void NewProp_bHasPortalGateways_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasPortalGateways;
	static void NewProp_bHasStrategicPoints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasStrategicPoints;
	static void NewProp_bHasDestructibleElements_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasDestructibleElements;
	static void NewProp_bHasElevationChanges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasElevationChanges;
	static void NewProp_bHasHazardZones_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasHazardZones;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityScale;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedComponents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IslandSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IslandHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeSinceLastUpdate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForAwakeningPhase, "ConfigureForAwakeningPhase" }, // 1196735173
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_ConfigureForConvergencePhase, "ConfigureForConvergencePhase" }, // 1278133890
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_GenerateIsland, "GenerateIsland" }, // 2782573947
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandSize, "GetIslandSize" }, // 3817104708
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_GetIslandType, "GetIslandType" }, // 2107302765
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_GetPCGComponent, "GetPCGComponent" }, // 3954334314
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_SetActivityScale, "SetActivityScale" }, // 3712997625
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_SetFullyEmerged, "SetFullyEmerged" }, // 2805997464
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandHeight, "SetIslandHeight" }, // 3655946303
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandPosition, "SetIslandPosition" }, // 3020357071
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandSize, "SetIslandSize" }, // 2013740390
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandType, "SetIslandType" }, // 2307613752
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_SetIslandVisibility, "SetIslandVisibility" }, // 1688064867
		{ &Z_Construct_UFunction_AAURACRONPCGIsland_UpdateForMapPhase, "UpdateForMapPhase" }, // 3321130488
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGIsland>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_CollisionComponent = { "CollisionComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, CollisionComponent), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionComponent_MetaData), NewProp_CollisionComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandMesh = { "IslandMesh", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, IslandMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandMesh_MetaData), NewProp_IslandMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandSettings = { "IslandSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, IslandSettings), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandSettings_MetaData), NewProp_IslandSettings_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasHealingFountain_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasHealingFountain = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasHealingFountain = { "bHasHealingFountain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasHealingFountain_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasHealingFountain_MetaData), NewProp_bHasHealingFountain_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasProtectiveBarrier_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasProtectiveBarrier = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasProtectiveBarrier = { "bHasProtectiveBarrier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasProtectiveBarrier_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasProtectiveBarrier_MetaData), NewProp_bHasProtectiveBarrier_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasAncientTree_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasAncientTree = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasAncientTree = { "bHasAncientTree", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasAncientTree_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasAncientTree_MetaData), NewProp_bHasAncientTree_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasResourceNodes_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasResourceNodes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasResourceNodes = { "bHasResourceNodes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasResourceNodes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasResourceNodes_MetaData), NewProp_bHasResourceNodes_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasCentralSpire_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasCentralSpire = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasCentralSpire = { "bHasCentralSpire", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasCentralSpire_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasCentralSpire_MetaData), NewProp_bHasCentralSpire_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasEnergyVortex_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasEnergyVortex = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasEnergyVortex = { "bHasEnergyVortex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasEnergyVortex_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasEnergyVortex_MetaData), NewProp_bHasEnergyVortex_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasAncientRunes_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasAncientRunes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasAncientRunes = { "bHasAncientRunes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasAncientRunes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasAncientRunes_MetaData), NewProp_bHasAncientRunes_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasPortalGateways_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasPortalGateways = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasPortalGateways = { "bHasPortalGateways", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasPortalGateways_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasPortalGateways_MetaData), NewProp_bHasPortalGateways_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasStrategicPoints_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasStrategicPoints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasStrategicPoints = { "bHasStrategicPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasStrategicPoints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasStrategicPoints_MetaData), NewProp_bHasStrategicPoints_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasDestructibleElements_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasDestructibleElements = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasDestructibleElements = { "bHasDestructibleElements", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasDestructibleElements_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasDestructibleElements_MetaData), NewProp_bHasDestructibleElements_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasElevationChanges_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasElevationChanges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasElevationChanges = { "bHasElevationChanges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasElevationChanges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasElevationChanges_MetaData), NewProp_bHasElevationChanges_MetaData) };
void Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasHazardZones_SetBit(void* Obj)
{
	((AAURACRONPCGIsland*)Obj)->bHasHazardZones = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasHazardZones = { "bHasHazardZones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGIsland), &Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasHazardZones_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasHazardZones_MetaData), NewProp_bHasHazardZones_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, IslandType), Z_Construct_UEnum_AURACRON_EAURACRONIslandType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandType_MetaData), NewProp_IslandType_MetaData) }; // 3618671819
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_ActivityScale = { "ActivityScale", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, ActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityScale_MetaData), NewProp_ActivityScale_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_GeneratedComponents_Inner = { "GeneratedComponents", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UActorComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_GeneratedComponents = { "GeneratedComponents", nullptr, (EPropertyFlags)0x0040008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, GeneratedComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedComponents_MetaData), NewProp_GeneratedComponents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandSize = { "IslandSize", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, IslandSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandSize_MetaData), NewProp_IslandSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandHeight = { "IslandHeight", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, IslandHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandHeight_MetaData), NewProp_IslandHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_TimeSinceLastUpdate = { "TimeSinceLastUpdate", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, TimeSinceLastUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeSinceLastUpdate_MetaData), NewProp_TimeSinceLastUpdate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_UpdateInterval = { "UpdateInterval", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGIsland, UpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateInterval_MetaData), NewProp_UpdateInterval_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_CollisionComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasHealingFountain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasProtectiveBarrier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasAncientTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasResourceNodes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasCentralSpire,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasEnergyVortex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasAncientRunes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasPortalGateways,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasStrategicPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasDestructibleElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasElevationChanges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_bHasHazardZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_ActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_AccumulatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_GeneratedComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_GeneratedComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_IslandHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_TimeSinceLastUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGIsland_Statics::NewProp_UpdateInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGIsland_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGIsland_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGIsland_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGIsland_Statics::ClassParams = {
	&AAURACRONPCGIsland::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGIsland_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGIsland_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGIsland_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGIsland_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGIsland()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGIsland.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGIsland.OuterSingleton, Z_Construct_UClass_AAURACRONPCGIsland_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGIsland.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGIsland);
AAURACRONPCGIsland::~AAURACRONPCGIsland() {}
// ********** End Class AAURACRONPCGIsland *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGIsland, AAURACRONPCGIsland::StaticClass, TEXT("AAURACRONPCGIsland"), &Z_Registration_Info_UClass_AAURACRONPCGIsland, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGIsland), 2859343742U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h__Script_AURACRON_2517739500(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
