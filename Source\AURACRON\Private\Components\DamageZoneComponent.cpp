// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "Components/DamageZoneComponent.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "Engine/World.h"
#include "Engine/DamageEvents.h"

// Sets default values for this component's properties
UDamageZoneComponent::UDamageZoneComponent()
{
    // Set this component to be initialized when the game starts, and to be ticked every frame
    PrimaryComponentTick.bCanEverTick = true;

    // Valores padrão
    SafeRadius = 10000.0f;
    WarningRadius = 10500.0f;
    DamagePerSecond = 10.0f;
    DamageScalingFactor = 1.5f;
    SetActive(false);
    AccumulatedTime = 0.0f;
    DamageInterval = 0.5f; // Aplicar dano a cada 0.5 segundos
    ZoneCenter = FVector::ZeroVector;
}

// Called when the game starts
void UDamageZoneComponent::BeginPlay()
{
    Super::BeginPlay();

    // Definir o centro da zona como a localização do ator proprietário
    if (GetOwner())
    {
        ZoneCenter = GetOwner()->GetActorLocation();
    }
    
    UE_LOG(LogTemp, Log, TEXT("DamageZoneComponent: Inicializado com raio seguro %.1f"), SafeRadius);
}

// Called every frame
void UDamageZoneComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    // Só processar se o componente estiver ativo
    if (IsActive())
    {
        ApplyDamageToPlayersOutsideSafeZone(DeltaTime);
    }
}

void UDamageZoneComponent::SetSafeRadius(float NewRadius)
{
    SafeRadius = NewRadius;
    UE_LOG(LogTemp, Verbose, TEXT("DamageZoneComponent: Raio seguro atualizado para %.1f"), SafeRadius);
}

void UDamageZoneComponent::SetDamagePerSecond(float NewDamagePerSecond)
{
    DamagePerSecond = NewDamagePerSecond;
    UE_LOG(LogTemp, Verbose, TEXT("DamageZoneComponent: Dano por segundo atualizado para %.1f"), DamagePerSecond);
}

void UDamageZoneComponent::SetDamageScalingFactor(float NewScalingFactor)
{
    DamageScalingFactor = NewScalingFactor;
    UE_LOG(LogTemp, Verbose, TEXT("DamageZoneComponent: Fator de escala atualizado para %.1f"), DamageScalingFactor);
}

void UDamageZoneComponent::SetWarningRadius(float NewWarningRadius)
{
    WarningRadius = NewWarningRadius;
    UE_LOG(LogTemp, Verbose, TEXT("DamageZoneComponent: Raio de aviso atualizado para %.1f"), WarningRadius);
}

void UDamageZoneComponent::SetActive(bool bNewActive, bool bReset)
{
    // Chamar implementação da classe pai
    Super::SetActive(bNewActive, bReset);

    // Usar API moderna do UE 5.6 para ativar/desativar componente
    SetComponentTickEnabled(bNewActive);
    UE_LOG(LogTemp, Log, TEXT("DamageZoneComponent: %s"), bNewActive ? TEXT("Ativado") : TEXT("Desativado"));
}

void UDamageZoneComponent::ApplyDamageToPlayersOutsideSafeZone(float DeltaTime)
{
    // Acumular tempo para aplicar dano no intervalo correto
    AccumulatedTime += DeltaTime;
    
    // Verificar se é hora de aplicar dano
    if (AccumulatedTime >= DamageInterval)
    {
        // Obter todos os jogadores
        TArray<AActor*> Players = GetAllPlayers();
        
        // Aplicar dano aos jogadores fora da zona segura
        for (AActor* Player : Players)
        {
            if (IsPlayerOutsideSafeZone(Player))
            {
                // Calcular dano baseado na distância
                float DamageMultiplier = CalculateDamageMultiplier(Player);
                float DamageAmount = DamagePerSecond * DamageInterval * DamageMultiplier;
                
                // Aplicar dano ao jogador
                FDamageEvent DamageEvent;
                Player->TakeDamage(DamageAmount, DamageEvent, nullptr, GetOwner());
                
                UE_LOG(LogTemp, Verbose, TEXT("DamageZoneComponent: Aplicando %.1f de dano ao jogador %s"), 
                       DamageAmount, *Player->GetName());
            }
        }
        
        // Resetar tempo acumulado
        AccumulatedTime = 0.0f;
    }
}

bool UDamageZoneComponent::IsPlayerOutsideSafeZone(AActor* Player) const
{
    if (!Player)
    {
        return false;
    }
    
    // Calcular distância do jogador ao centro da zona
    float Distance = FVector::Dist(Player->GetActorLocation(), ZoneCenter);
    
    // Verificar se está fora da zona segura
    return Distance > SafeRadius;
}

float UDamageZoneComponent::CalculateDamageMultiplier(AActor* Player) const
{
    if (!Player)
    {
        return 1.0f;
    }
    
    // Calcular distância do jogador ao centro da zona
    float Distance = FVector::Dist(Player->GetActorLocation(), ZoneCenter);
    
    // Calcular quanto o jogador está além do limite seguro
    float ExcessDistance = Distance - SafeRadius;
    
    // Normalizar para um valor entre 0 e 1, assumindo que o jogador não estará mais longe que 2x o raio seguro
    float NormalizedExcess = FMath::Clamp(ExcessDistance / SafeRadius, 0.0f, 1.0f);
    
    // Aplicar fator de escala
    return 1.0f + (NormalizedExcess * DamageScalingFactor);
}

TArray<AActor*> UDamageZoneComponent::GetAllPlayers() const
{
    TArray<AActor*> Players;
    
    UWorld* World = GetWorld();
    if (!World)
    {
        return Players;
    }
    
    // Obter todos os pawns controlados por jogadores
    for (FConstPlayerControllerIterator It = World->GetPlayerControllerIterator(); It; ++It)
    {
        APlayerController* PC = It->Get();
        if (PC && PC->GetPawn())
        {
            Players.Add(PC->GetPawn());
        }
    }
    
    return Players;
}

// ========================================
// IMPLEMENTAÇÃO DA FUNÇÃO QUE ESTAVA FALTANDO - UE 5.6 MODERN APIS
// ========================================

void UDamageZoneComponent::SetDamageZoneActive(bool bActive)
{
    // Implementação robusta para ativar/desativar zona de dano
    SetActive(bActive);

    if (bActive)
    {
        // Ativar zona de dano
        AccumulatedTime = 0.0f;

        // Resetar centro da zona para posição atual do owner
        if (GetOwner())
        {
            ZoneCenter = GetOwner()->GetActorLocation();
        }

        UE_LOG(LogTemp, Log, TEXT("UDamageZoneComponent::SetDamageZoneActive - Damage zone activated at location %s"),
               *ZoneCenter.ToString());
    }
    else
    {
        // Desativar zona de dano
        UE_LOG(LogTemp, Log, TEXT("UDamageZoneComponent::SetDamageZoneActive - Damage zone deactivated"));
    }
}