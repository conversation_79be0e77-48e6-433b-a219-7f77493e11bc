// AURACRONMovementComponent.cpp
// Sistema de Sígilos AURACRON - Implementação do Componente de Movimento Customizado UE 5.6

#include "Components/AURACRONMovementComponent.h"
#include "Components/AURACRONSigilComponent.h"
#include "Net/UnrealNetwork.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"

// Includes para integração com sistemas existentes
#include "PCG/AURACRONPCGTrail.h"
#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGSubsystem.h"

// Includes para APIs modernas UE 5.6
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayTagContainer.h"

// Logging
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONMovement, Log, All);

UAURACRONMovementComponent::UAURACRONMovementComponent()
{
    // Configurações básicas
    CurrentMovementState = EAURACRONMovementState::Normal;
    CurrentEnvironment = EAURACRONEnvironmentType::RadiantPlains;
    
    // Fluxo Prismal
    bIsInPrismalFlow = false;
    PrismalFlowDirection = FVector::ZeroVector;
    PrismalFlowSpeed = 1.0f;
    FlowSpeedMultiplier = 1.5f;
    
    // Dash
    bIsDashing = false;
    DashTimeRemaining = 0.0f;
    DashDirection = FVector::ZeroVector;
    DashSpeed = 0.0f;
    DashCooldown = 3.0f;
    DashCooldownRemaining = 0.0f;
    
    // Configurações padrão de ambiente
    FAURACRONEnvironmentMovementConfig RadiantConfig;
    RadiantConfig.SpeedMultiplier = 1.0f;
    RadiantConfig.AccelerationMultiplier = 1.0f;
    RadiantConfig.JumpForceMultiplier = 1.0f;
    RadiantConfig.bAllowsFlight = false;
    EnvironmentConfigs.Add(EAURACRONEnvironmentType::RadiantPlains, RadiantConfig);

    // Inicializar controle de transição
    bCanTransitionEnvironments = false;
    
    FAURACRONEnvironmentMovementConfig ZephyrConfig;
    ZephyrConfig.SpeedMultiplier = 1.2f;
    ZephyrConfig.AccelerationMultiplier = 1.3f;
    ZephyrConfig.JumpForceMultiplier = 1.5f;
    ZephyrConfig.bAllowsFlight = true;
    EnvironmentConfigs.Add(EAURACRONEnvironmentType::ZephyrFirmament, ZephyrConfig);
    
    FAURACRONEnvironmentMovementConfig PurgatoryConfig;
    PurgatoryConfig.SpeedMultiplier = 0.9f;
    PurgatoryConfig.AccelerationMultiplier = 0.8f;
    PurgatoryConfig.JumpForceMultiplier = 0.8f;
    PurgatoryConfig.bAllowsFlight = false;
    EnvironmentConfigs.Add(EAURACRONEnvironmentType::PurgatoryRealm, PurgatoryConfig);
    
    // Inicializar arrays
    ActiveSpeedModifiers.Empty();
}

void UAURACRONMovementComponent::BeginPlay()
{
    Super::BeginPlay();
    
    // Armazenar valores base
    BaseMaxWalkSpeed = MaxWalkSpeed;
    BaseMaxAcceleration = MaxAcceleration;
    
    // Obter referência ao componente de Sígilos
    if (ACharacter* Character = Cast<ACharacter>(GetOwner()))
    {
        SigilComponent = Character->FindComponentByClass<UAURACRONSigilComponent>();
    }
    
    // Aplicar configurações do ambiente inicial
    ApplyEnvironmentSettings();
}

void UAURACRONMovementComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Atualizar apenas no servidor
    if (GetOwner()->HasAuthority())
    {
        UpdateMovementForState(DeltaTime);
        UpdateSpeedModifiers(DeltaTime);
    }
}

void UAURACRONMovementComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Usar replicação condicional moderna do UE 5.6 para otimização de performance
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, CurrentMovementState, COND_None);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, CurrentEnvironment, COND_InitialOnly);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, bIsInPrismalFlow, COND_None);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, PrismalFlowDirection, COND_SkipOwner);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, PrismalFlowSpeed, COND_SkipOwner);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, bIsDashing, COND_None);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, DashTimeRemaining, COND_OwnerOnly);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, DashDirection, COND_SkipOwner);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, DashSpeed, COND_SkipOwner);
    DOREPLIFETIME_CONDITION(UAURACRONMovementComponent, DashCooldownRemaining, COND_OwnerOnly);

    UE_LOG(LogAURACRONMovement, Log, TEXT("Propriedades de replicação configuradas com otimizações UE 5.6"));
}

float UAURACRONMovementComponent::GetMaxSpeed() const
{
    float Speed = Super::GetMaxSpeed();
    
    // Aplicar multiplicadores baseados no estado
    switch (CurrentMovementState)
    {
        case EAURACRONMovementState::PrismalFlow:
            Speed *= FlowSpeedMultiplier * PrismalFlowSpeed;
            break;
        case EAURACRONMovementState::SigilDash:
            Speed = DashSpeed;
            break;
        case EAURACRONMovementState::Stunned:
        case EAURACRONMovementState::Rooted:
            Speed = 0.0f;
            break;
        default:
            break;
    }
    
    // Aplicar modificadores de ambiente
    const FAURACRONEnvironmentMovementConfig* Config = EnvironmentConfigs.Find(CurrentEnvironment);
    if (Config)
    {
        Speed *= Config->SpeedMultiplier;
    }
    
    // Aplicar modificadores temporários
    Speed *= CalculateTotalSpeedMultiplier();
    
    return Speed;
}

float UAURACRONMovementComponent::GetMaxAcceleration() const
{
    float CurrentAcceleration = Super::GetMaxAcceleration();

    // Aplicar modificadores de ambiente
    const FAURACRONEnvironmentMovementConfig* Config = EnvironmentConfigs.Find(CurrentEnvironment);
    if (Config)
    {
        CurrentAcceleration *= Config->AccelerationMultiplier;
    }

    return CurrentAcceleration;
}

float UAURACRONMovementComponent::GetMaxBrakingDeceleration() const
{
    float Deceleration = Super::GetMaxBrakingDeceleration();
    
    // Aplicar modificadores baseados no estado
    if (CurrentMovementState == EAURACRONMovementState::PrismalFlow)
    {
        Deceleration *= 0.5f; // Menos frenagem no fluxo
    }
    
    return Deceleration;
}

void UAURACRONMovementComponent::SetMovementState_Implementation(EAURACRONMovementState NewState)
{
    if (CurrentMovementState == NewState)
    {
        return;
    }
    
    EAURACRONMovementState OldState = CurrentMovementState;
    CurrentMovementState = NewState;
    
    // Chamar evento
    OnMovementStateChanged(OldState, NewState);
}

EAURACRONMovementState UAURACRONMovementComponent::GetMovementState() const
{
    return CurrentMovementState;
}

void UAURACRONMovementComponent::SetCurrentEnvironment_Implementation(EAURACRONEnvironmentType Environment)
{
    if (CurrentEnvironment == Environment)
    {
        return;
    }
    
    CurrentEnvironment = Environment;
    ApplyEnvironmentSettings();
}

EAURACRONEnvironmentType UAURACRONMovementComponent::GetCurrentEnvironment() const
{
    return CurrentEnvironment;
}

void UAURACRONMovementComponent::EnterPrismalFlow_Implementation(FVector FlowDirection, float FlowSpeed)
{
    bIsInPrismalFlow = true;
    PrismalFlowDirection = FlowDirection.GetSafeNormal();
    PrismalFlowSpeed = FMath::Max(FlowSpeed, 0.1f);
    
    SetMovementState(EAURACRONMovementState::PrismalFlow);
    
    // Chamar evento
    OnEnteredPrismalFlow(PrismalFlowDirection, PrismalFlowSpeed);
}

void UAURACRONMovementComponent::ExitPrismalFlow_Implementation()
{
    if (!bIsInPrismalFlow)
    {
        return;
    }
    
    bIsInPrismalFlow = false;
    PrismalFlowDirection = FVector::ZeroVector;
    PrismalFlowSpeed = 1.0f;
    
    SetMovementState(EAURACRONMovementState::Normal);
    
    // Chamar evento
    OnExitedPrismalFlow();
}

bool UAURACRONMovementComponent::IsInPrismalFlow() const
{
    return bIsInPrismalFlow;
}

void UAURACRONMovementComponent::PerformSigilDash_Implementation(FVector Direction, float Distance, float Duration)
{
    if (!CanPerformDash())
    {
        return;
    }
    
    // Configurar dash
    bIsDashing = true;
    DashTimeRemaining = Duration;
    DashDirection = Direction.GetSafeNormal();
    DashSpeed = Distance / Duration;
    DashCooldownRemaining = DashCooldown;
    
    SetMovementState(EAURACRONMovementState::SigilDash);
    
    // Chamar evento
    OnSigilDashPerformed(Direction, Distance);
}

bool UAURACRONMovementComponent::CanPerformDash() const
{
    return DashCooldownRemaining <= 0.0f && !bIsDashing && 
           CurrentMovementState != EAURACRONMovementState::Stunned &&
           CurrentMovementState != EAURACRONMovementState::Rooted;
}

void UAURACRONMovementComponent::ApplySpeedModifier_Implementation(float Multiplier, float Duration, FName ModifierName)
{
    // Remover modificador existente com o mesmo nome
    RemoveSpeedModifier(ModifierName);
    
    // Adicionar novo modificador
    ActiveSpeedModifiers.Add(FSpeedModifier(Multiplier, Duration, ModifierName));
}

void UAURACRONMovementComponent::RemoveSpeedModifier_Implementation(FName ModifierName)
{
    ActiveSpeedModifiers.RemoveAll([ModifierName](const FSpeedModifier& Modifier)
    {
        return Modifier.Name == ModifierName;
    });
}

float UAURACRONMovementComponent::GetModifiedSpeed() const
{
    return GetMaxSpeed();
}

void UAURACRONMovementComponent::UpdateMovementForState(float DeltaTime)
{
    // Atualizar dash
    if (bIsDashing)
    {
        DashTimeRemaining = FMath::Max(0.0f, DashTimeRemaining - DeltaTime);
        if (DashTimeRemaining <= 0.0f)
        {
            bIsDashing = false;
            SetMovementState(EAURACRONMovementState::Normal);
        }
    }
    
    // Atualizar cooldown do dash
    if (DashCooldownRemaining > 0.0f)
    {
        DashCooldownRemaining = FMath::Max(0.0f, DashCooldownRemaining - DeltaTime);
    }
}

void UAURACRONMovementComponent::UpdateSpeedModifiers(float DeltaTime)
{
    // Atualizar tempo dos modificadores
    for (int32 i = ActiveSpeedModifiers.Num() - 1; i >= 0; i--)
    {
        ActiveSpeedModifiers[i].TimeRemaining -= DeltaTime;
        if (ActiveSpeedModifiers[i].TimeRemaining <= 0.0f)
        {
            ActiveSpeedModifiers.RemoveAt(i);
        }
    }
}

float UAURACRONMovementComponent::CalculateTotalSpeedMultiplier() const
{
    float TotalMultiplier = 1.0f;
    
    for (const FSpeedModifier& Modifier : ActiveSpeedModifiers)
    {
        TotalMultiplier *= Modifier.Multiplier;
    }
    
    return TotalMultiplier;
}

void UAURACRONMovementComponent::ApplyEnvironmentSettings()
{
    const FAURACRONEnvironmentMovementConfig* Config = EnvironmentConfigs.Find(CurrentEnvironment);
    if (!Config)
    {
        return;
    }
    
    // Aplicar configurações do ambiente
    MaxWalkSpeed = BaseMaxWalkSpeed * Config->SpeedMultiplier;
    MaxAcceleration = BaseMaxAcceleration * Config->AccelerationMultiplier;
    JumpZVelocity = 600.0f * Config->JumpForceMultiplier;
    
    // Configurar voo se permitido
    if (Config->bAllowsFlight)
    {
        SetMovementMode(MOVE_Flying);
    }
    else if (MovementMode == MOVE_Flying)
    {
        SetMovementMode(MOVE_Walking);
    }
}

void UAURACRONMovementComponent::OnRep_MovementState()
{
    // Lógica robusta quando o estado de movimento é replicado
    UE_LOG(LogAURACRONMovement, Log, TEXT("MovementState replicado: %s"),
           *UEnum::GetValueAsString(CurrentMovementState));

    // Validação robusta do estado
    if (!ensure(IsValid(GetOwner())))
    {
        UE_LOG(LogAURACRONMovement, Error, TEXT("Owner inválido durante OnRep_MovementState"));
        return;
    }

    // Aplicar configurações específicas do estado
    ApplyMovementStateSettings();

    // Atualizar efeitos visuais baseados no estado
    UpdateMovementVisualEffects();

    // Sincronizar com sistemas de trilhos se necessário
    if (CurrentMovementState == EAURACRONMovementState::PrismalFlow)
    {
        SynchronizeWithPrismalFlowSystems();
    }

    // Integrar com sistemas PCG existentes
    IntegrateWithPCGSystems();

    // Notificar outros componentes sobre mudança de estado
    OnMovementStateChanged.Broadcast(CurrentMovementState);

    // Aplicar validação anti-cheat server-side
    if (GetOwner()->HasAuthority())
    {
        ValidateMovementState();
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("OnRep_MovementState processado com sucesso"));
}

void UAURACRONMovementComponent::OnRep_CurrentEnvironment()
{
    // Lógica robusta quando o ambiente atual é replicado
    UE_LOG(LogAURACRONMovement, Log, TEXT("CurrentEnvironment replicado: %s"),
           *UEnum::GetValueAsString(CurrentEnvironment));

    // Validação robusta do ambiente
    if (!ensure(IsValid(GetOwner())))
    {
        UE_LOG(LogAURACRONMovement, Error, TEXT("Owner inválido durante OnRep_CurrentEnvironment"));
        return;
    }

    // Aplicar configurações específicas do ambiente
    ApplyEnvironmentSettings();

    // Integrar com sistema PCG de ambientes existente
    IntegrateWithEnvironmentSystems();

    // Atualizar efeitos visuais do ambiente
    UpdateEnvironmentVisualEffects();

    // Sincronizar com trilhos do ambiente atual
    SynchronizeWithEnvironmentTrails();

    // Aplicar mecânicas específicas do ambiente
    ApplyEnvironmentSpecificMechanics();

    // Notificar outros sistemas sobre mudança de ambiente
    OnEnvironmentChanged.Broadcast(CurrentEnvironment);

    UE_LOG(LogAURACRONMovement, Log, TEXT("OnRep_CurrentEnvironment processado com sucesso"));
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES AUXILIARES - UE 5.6 APIS MODERNAS
// ========================================

void UAURACRONMovementComponent::ApplyMovementStateSettings()
{
    // Aplicar configurações específicas baseadas no estado atual
    if (!ensure(IsValid(GetOwner())))
    {
        return;
    }

    switch (CurrentMovementState)
    {
        case EAURACRONMovementState::Normal:
            // Restaurar configurações padrão
            MaxWalkSpeed = BaseMaxWalkSpeed;
            MaxAcceleration = 2048.0f;
            BrakingDecelerationWalking = 2000.0f;
            break;

        case EAURACRONMovementState::PrismalFlow:
            // Aplicar modificadores do fluxo prismal
            MaxWalkSpeed = BaseMaxWalkSpeed * FlowSpeedMultiplier * PrismalFlowSpeed;
            MaxAcceleration = 4096.0f; // Aceleração aumentada no fluxo
            BrakingDecelerationWalking = 1000.0f; // Frenagem reduzida no fluxo
            break;

        case EAURACRONMovementState::SigilDash:
            // Configurações para dash de sígilo
            MaxWalkSpeed = DashSpeed;
            MaxAcceleration = 8192.0f; // Aceleração máxima para dash
            BrakingDecelerationWalking = 4000.0f;
            break;

        case EAURACRONMovementState::EnvironmentBoost:
            // Aplicar boost de ambiente (trilhos)
            ApplyEnvironmentBoostSettings();
            break;

        case EAURACRONMovementState::Stunned:
        case EAURACRONMovementState::Rooted:
            // Movimento impedido
            MaxWalkSpeed = 0.0f;
            MaxAcceleration = 0.0f;
            break;

        default:
            UE_LOG(LogAURACRONMovement, Warning, TEXT("Estado de movimento não reconhecido: %d"),
                   static_cast<int32>(CurrentMovementState));
            break;
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Configurações de movimento aplicadas para estado: %s"),
           *UEnum::GetValueAsString(CurrentMovementState));
}

void UAURACRONMovementComponent::UpdateMovementVisualEffects()
{
    // Atualizar efeitos visuais baseados no estado de movimento
    if (!ensure(IsValid(GetOwner())))
    {
        return;
    }

    // Obter componente Niagara se existir
    if (UNiagaraComponent* NiagaraComp = GetOwner()->FindComponentByClass<UNiagaraComponent>())
    {
        switch (CurrentMovementState)
        {
            case EAURACRONMovementState::PrismalFlow:
                // Ativar efeitos do fluxo prismal
                NiagaraComp->SetFloatParameter(FName("FlowIntensity"), PrismalFlowSpeed);
                NiagaraComp->SetVectorParameter(FName("FlowDirection"), PrismalFlowDirection);
                NiagaraComp->SetBoolParameter(FName("InPrismalFlow"), true);
                break;

            case EAURACRONMovementState::SigilDash:
                // Ativar efeitos de dash
                NiagaraComp->SetFloatParameter(FName("DashIntensity"), DashSpeed / 1000.0f);
                NiagaraComp->SetVectorParameter(FName("DashDirection"), DashDirection);
                NiagaraComp->SetBoolParameter(FName("IsDashing"), true);
                break;

            case EAURACRONMovementState::Normal:
            default:
                // Desativar efeitos especiais
                NiagaraComp->SetBoolParameter(FName("InPrismalFlow"), false);
                NiagaraComp->SetBoolParameter(FName("IsDashing"), false);
                break;
        }
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Efeitos visuais de movimento atualizados"));
}

void UAURACRONMovementComponent::SynchronizeWithPrismalFlowSystems()
{
    // Sincronizar com sistemas de fluxo prismal existentes
    if (!ensure(IsValid(GetOwner())) || !ensure(GetWorld()))
    {
        return;
    }

    // Obter subsistema PCG para integração
    if (UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>())
    {
        // Notificar subsistema sobre entrada no fluxo prismal
        if (bIsInPrismalFlow)
        {
            PCGSubsystem->OnCharacterEnteredPrismalFlow(Cast<ACharacter>(GetOwner()),
                                                       PrismalFlowDirection, PrismalFlowSpeed);
        }
        else
        {
            PCGSubsystem->OnCharacterExitedPrismalFlow(Cast<ACharacter>(GetOwner()));
        }
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Sincronização com sistemas de fluxo prismal concluída"));
}

void UAURACRONMovementComponent::IntegrateWithPCGSystems()
{
    // Integrar com sistemas PCG existentes
    if (!ensure(IsValid(GetOwner())) || !ensure(GetWorld()))
    {
        return;
    }

    // Obter subsistema PCG
    if (UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>())
    {
        // Notificar sobre mudança de estado de movimento
        PCGSubsystem->OnCharacterMovementStateChanged(Cast<ACharacter>(GetOwner()), CurrentMovementState);

        // Verificar se está em trilho ativo
        CheckForActiveTrails();
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Integração com sistemas PCG concluída"));
}

void UAURACRONMovementComponent::IntegrateWithEnvironmentSystems()
{
    // Integrar com sistema PCG de ambientes existente
    if (!ensure(IsValid(GetOwner())) || !ensure(GetWorld()))
    {
        return;
    }

    // Buscar ambiente PCG ativo
    TArray<AActor*> EnvironmentActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGEnvironment::StaticClass(), EnvironmentActors);

    for (AActor* Actor : EnvironmentActors)
    {
        if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Actor))
        {
            // Verificar se o personagem está neste ambiente
            if (Environment->IsCharacterInEnvironment(Cast<ACharacter>(GetOwner())))
            {
                // Aplicar efeitos específicos do ambiente
                Environment->ApplyEnvironmentEffectsToCharacter(Cast<ACharacter>(GetOwner()));
                break;
            }
        }
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Integração com sistemas de ambiente concluída"));
}

void UAURACRONMovementComponent::UpdateEnvironmentVisualEffects()
{
    // Atualizar efeitos visuais específicos do ambiente
    if (!ensure(IsValid(GetOwner())))
    {
        return;
    }

    // Obter componente Niagara para efeitos de ambiente
    if (UNiagaraComponent* NiagaraComp = GetOwner()->FindComponentByClass<UNiagaraComponent>())
    {
        switch (CurrentEnvironment)
        {
            case EAURACRONEnvironmentType::RadiantPlains:
                // Efeitos dourados da Planície Radiante
                NiagaraComp->SetColorParameter(FName("EnvironmentColor"), FLinearColor(1.0f, 0.8f, 0.3f, 1.0f));
                NiagaraComp->SetFloatParameter(FName("EnvironmentIntensity"), 1.0f);
                break;

            case EAURACRONEnvironmentType::ZephyrFirmament:
                // Efeitos azuis do Firmamento Zephyr
                NiagaraComp->SetColorParameter(FName("EnvironmentColor"), FLinearColor(0.3f, 0.7f, 1.0f, 1.0f));
                NiagaraComp->SetFloatParameter(FName("EnvironmentIntensity"), 0.8f);
                break;

            case EAURACRONEnvironmentType::PurgatoryRealm:
                // Efeitos roxos do Reino Purgatório
                NiagaraComp->SetColorParameter(FName("EnvironmentColor"), FLinearColor(0.8f, 0.3f, 1.0f, 1.0f));
                NiagaraComp->SetFloatParameter(FName("EnvironmentIntensity"), 1.2f);
                break;

            default:
                // Efeitos neutros
                NiagaraComp->SetColorParameter(FName("EnvironmentColor"), FLinearColor::White);
                NiagaraComp->SetFloatParameter(FName("EnvironmentIntensity"), 0.5f);
                break;
        }
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Efeitos visuais de ambiente atualizados para: %s"),
           *UEnum::GetValueAsString(CurrentEnvironment));
}

void UAURACRONMovementComponent::SynchronizeWithEnvironmentTrails()
{
    // Sincronizar com trilhos do ambiente atual
    if (!ensure(IsValid(GetOwner())) || !ensure(GetWorld()))
    {
        return;
    }

    // Buscar trilhos ativos no ambiente atual
    TArray<AActor*> TrailActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGTrail::StaticClass(), TrailActors);

    for (AActor* Actor : TrailActors)
    {
        if (AAURACRONPCGTrail* Trail = Cast<AAURACRONPCGTrail>(Actor))
        {
            // Verificar se o personagem está no trilho
            if (Trail->IsPlayerInTrail(Cast<ACharacter>(GetOwner())))
            {
                // Aplicar efeitos do trilho
                ApplyTrailEffects(Trail);
                break;
            }
        }
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Sincronização com trilhos de ambiente concluída"));
}

void UAURACRONMovementComponent::ApplyEnvironmentSpecificMechanics()
{
    // Aplicar mecânicas específicas do ambiente conforme documento de design
    if (!ensure(IsValid(GetOwner())))
    {
        return;
    }

    switch (CurrentEnvironment)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Planície Radiante: Respiradouros Geotermais
            CheckForGeothermalVents();
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Firmamento Zephyr: Correntes de vento, permite voo
            ApplyWindCurrents();
            SetMovementMode(MOVE_Flying);
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Reino Purgatório: Física alterada, zonas de distorção temporal
            ApplyAlteredPhysics();
            CheckForTemporalDistortionZones();
            break;

        default:
            // Ambiente padrão
            SetMovementMode(MOVE_Walking);
            break;
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Mecânicas específicas aplicadas para ambiente: %s"),
           *UEnum::GetValueAsString(CurrentEnvironment));
}

void UAURACRONMovementComponent::ValidateMovementState()
{
    // Sistema anti-cheat server-side para validação de movimento
    if (!ensure(GetOwner()->HasAuthority()))
    {
        return;
    }

    // Validar velocidade máxima
    float CurrentSpeed = Velocity.Size();
    float MaxAllowedSpeed = GetMaxSpeed() * 1.1f; // 10% de tolerância

    if (CurrentSpeed > MaxAllowedSpeed)
    {
        UE_LOG(LogAURACRONMovement, Warning, TEXT("Velocidade suspeita detectada: %.2f (máx: %.2f) para %s"),
               CurrentSpeed, MaxAllowedSpeed, *GetOwner()->GetName());

        // Corrigir velocidade
        Velocity = Velocity.GetSafeNormal() * MaxAllowedSpeed;

        // Log para sistema anti-cheat
        LogSuspiciousActivity(TEXT("Speed"), CurrentSpeed, MaxAllowedSpeed);
    }

    // Validar estado de movimento
    if (CurrentMovementState == EAURACRONMovementState::SigilDash && !bIsDashing)
    {
        UE_LOG(LogAURACRONMovement, Warning, TEXT("Estado de dash inconsistente para %s"), *GetOwner()->GetName());
        SetMovementState(EAURACRONMovementState::Normal);
    }

    UE_LOG(LogAURACRONMovement, VeryVerbose, TEXT("Validação de movimento concluída para %s"), *GetOwner()->GetName());
}

// ========================================
// FUNÇÕES AUXILIARES ESPECÍFICAS DE AMBIENTE
// ========================================

void UAURACRONMovementComponent::ApplyEnvironmentBoostSettings()
{
    // Aplicar configurações de boost de ambiente (trilhos)
    const FAURACRONEnvironmentMovementConfig* Config = EnvironmentConfigs.Find(CurrentEnvironment);
    if (Config)
    {
        MaxWalkSpeed = BaseMaxWalkSpeed * Config->SpeedMultiplier * 1.3f; // Boost adicional
        MaxAcceleration = 3072.0f * Config->AccelerationMultiplier;
        JumpZVelocity = 600.0f * Config->JumpForceMultiplier;

        if (Config->bAllowsFlight)
        {
            SetMovementMode(MOVE_Flying);
        }
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Configurações de boost de ambiente aplicadas"));
}

void UAURACRONMovementComponent::CheckForActiveTrails()
{
    // Verificar trilhos ativos próximos
    if (!ensure(IsValid(GetOwner())) || !ensure(GetWorld()))
    {
        return;
    }

    TArray<AActor*> TrailActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGTrail::StaticClass(), TrailActors);

    for (AActor* Actor : TrailActors)
    {
        if (AAURACRONPCGTrail* Trail = Cast<AAURACRONPCGTrail>(Actor))
        {
            float Distance = FVector::Dist(GetOwner()->GetActorLocation(), Trail->GetActorLocation());
            if (Distance <= 500.0f) // 5 metros de proximidade
            {
                // Notificar sobre trilho próximo
                OnNearTrail.Broadcast(Trail);
            }
        }
    }
}

void UAURACRONMovementComponent::ApplyTrailEffects(AAURACRONPCGTrail* Trail)
{
    // Aplicar efeitos específicos do trilho
    if (!ensure(IsValid(Trail)))
    {
        return;
    }

    // Obter tipo do trilho e aplicar efeitos correspondentes
    EAURACRONTrailType TrailType = Trail->GetTrailType();

    switch (TrailType)
    {
        case EAURACRONTrailType::Solar:
            // Solar Trail: boost de velocidade + regeneração
            SetMovementState(EAURACRONMovementState::EnvironmentBoost);
            ApplySpeedModifier(1.3f, 5.0f, FName("SolarTrailBoost"));
            break;

        case EAURACRONTrailType::Axis:
            // Axis Trail: transição entre ambientes
            EnableEnvironmentTransition();
            break;

        case EAURACRONTrailType::Lunar:
            // Lunar Trail: furtividade + visão aprimorada
            ApplySpeedModifier(1.2f, 5.0f, FName("LunarTrailBoost"));
            break;

        default:
            break;
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Efeitos do trilho %s aplicados"),
           *UEnum::GetValueAsString(TrailType));
}

void UAURACRONMovementComponent::CheckForGeothermalVents()
{
    // Verificar respiradouros geotermais na Planície Radiante
    if (!ensure(IsValid(GetOwner())) || !ensure(GetWorld()))
    {
        return;
    }

    // Buscar ambiente PCG da Planície Radiante
    TArray<AActor*> EnvironmentActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGEnvironment::StaticClass(), EnvironmentActors);

    for (AActor* Actor : EnvironmentActors)
    {
        if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Actor))
        {
            // Verificar se há respiradouros ativos próximos
            if (Environment->HasActiveGeothermalVents())
            {
                FVector PlayerLocation = GetOwner()->GetActorLocation();
                if (Environment->IsNearGeothermalVent(PlayerLocation, 300.0f)) // 3 metros
                {
                    // Aplicar impulso de mobilidade
                    LaunchCharacter(FVector(0, 0, 1200.0f), false, true);
                    UE_LOG(LogAURACRONMovement, Log, TEXT("Respiradouro geotermal ativado para %s"), *GetOwner()->GetName());
                }
            }
        }
    }
}

void UAURACRONMovementComponent::ApplyWindCurrents()
{
    // Aplicar correntes de vento no Firmamento Zephyr
    if (!ensure(IsValid(GetOwner())))
    {
        return;
    }

    // Simular correntes de vento baseadas na posição
    FVector PlayerLocation = GetOwner()->GetActorLocation();
    float WindStrength = FMath::Sin(GetWorld()->GetTimeSeconds() * 0.5f) * 200.0f + 300.0f;
    FVector WindDirection = FVector(
        FMath::Cos(PlayerLocation.X * 0.001f),
        FMath::Sin(PlayerLocation.Y * 0.001f),
        0.1f
    ).GetSafeNormal();

    // Aplicar força do vento
    AddImpulse(WindDirection * WindStrength, true);

    UE_LOG(LogAURACRONMovement, VeryVerbose, TEXT("Correntes de vento aplicadas: força %.2f"), WindStrength);
}

void UAURACRONMovementComponent::ApplyAlteredPhysics()
{
    // Aplicar física alterada no Reino Purgatório
    if (!ensure(IsValid(GetOwner())))
    {
        return;
    }

    // Reduzir gravidade
    GravityScale = 0.7f;

    // Alterar propriedades de movimento
    MaxWalkSpeed = BaseMaxWalkSpeed * 0.9f; // Movimento mais lento
    JumpZVelocity = 800.0f; // Pulos mais altos
    AirControl = 0.5f; // Maior controle no ar

    // Aplicar distorção temporal no movimento
    float TemporalDistortion = 1.0f + FMath::Sin(GetWorld()->GetTimeSeconds() * 0.3f) * 0.2f;
    MaxWalkSpeed *= TemporalDistortion;

    UE_LOG(LogAURACRONMovement, Log, TEXT("Física alterada aplicada no Reino Purgatório"));
}

void UAURACRONMovementComponent::CheckForTemporalDistortionZones()
{
    // Verificar zonas de distorção temporal
    if (!ensure(IsValid(GetOwner())) || !ensure(GetWorld()))
    {
        return;
    }

    // Buscar ambiente PCG do Reino Purgatório
    TArray<AActor*> EnvironmentActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGEnvironment::StaticClass(), EnvironmentActors);

    for (AActor* Actor : EnvironmentActors)
    {
        if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Actor))
        {
            FVector PlayerLocation = GetOwner()->GetActorLocation();
            if (Environment->IsInTemporalDistortionZone(PlayerLocation))
            {
                // Aplicar efeitos de distorção temporal
                float DistortionIntensity = Environment->GetTemporalDistortionIntensity(PlayerLocation);
                ApplyTemporalDistortion(DistortionIntensity);
            }
        }
    }
}

void UAURACRONMovementComponent::EnableEnvironmentTransition()
{
    // Habilitar transição entre ambientes (Axis Trail)
    if (!ensure(IsValid(GetOwner())))
    {
        return;
    }

    // Marcar personagem como pronto para transição
    bCanTransitionEnvironments = true;

    // Configurar timer para desabilitar transição
    if (UWorld* World = GetWorld())
    {
        FTimerHandle TransitionTimer;
        World->GetTimerManager().SetTimer(TransitionTimer, [this]()
        {
            bCanTransitionEnvironments = false;
        }, 10.0f, false); // 10 segundos para usar a transição
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Transição de ambiente habilitada para %s"), *GetOwner()->GetName());
}

void UAURACRONMovementComponent::ApplyTemporalDistortion(float DistortionIntensity)
{
    // Aplicar efeitos de distorção temporal
    if (!ensure(IsValid(GetOwner())))
    {
        return;
    }

    // Modificar velocidade baseada na distorção
    float SpeedModifier = 1.0f + (DistortionIntensity - 1.0f) * 0.5f;
    MaxWalkSpeed = BaseMaxWalkSpeed * SpeedModifier;

    // Aplicar efeitos visuais de distorção
    if (UNiagaraComponent* NiagaraComp = GetOwner()->FindComponentByClass<UNiagaraComponent>())
    {
        NiagaraComp->SetFloatParameter(FName("TemporalDistortion"), DistortionIntensity);
        NiagaraComp->SetBoolParameter(FName("InDistortionZone"), true);
    }

    UE_LOG(LogAURACRONMovement, Log, TEXT("Distorção temporal aplicada: intensidade %.2f"), DistortionIntensity);
}

void UAURACRONMovementComponent::LogSuspiciousActivity(const FString& ActivityType, float CurrentValue, float MaxValue)
{
    // Log de atividade suspeita para sistema anti-cheat
    if (!ensure(GetOwner()->HasAuthority()))
    {
        return;
    }

    FString LogMessage = FString::Printf(TEXT("ANTI-CHEAT: %s - Jogador: %s, Valor: %.2f, Máximo: %.2f, Tempo: %.2f"),
                                        *ActivityType, *GetOwner()->GetName(), CurrentValue, MaxValue, GetWorld()->GetTimeSeconds());

    UE_LOG(LogAURACRONMovement, Warning, TEXT("%s"), *LogMessage);

    // Aqui poderia integrar com sistema de anti-cheat externo
    // Por exemplo, enviar para servidor de monitoramento
}

// ========================================
// IMPLEMENTAÇÃO COMPLETA - UE 5.6 PRODUCTION READY
// ========================================
