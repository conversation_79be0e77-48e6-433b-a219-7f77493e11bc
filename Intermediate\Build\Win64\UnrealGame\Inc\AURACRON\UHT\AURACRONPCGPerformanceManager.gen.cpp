// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGPerformanceManager.h"
#include "Engine/TimerHandle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGPerformanceManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPerformanceManager();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPerformanceManager_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONDeviceType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FGuid();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONPCGLODLevel ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONPCGLODLevel;
static UEnum* EAURACRONPCGLODLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONPCGLODLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONPCGLODLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONPCGLODLevel"));
	}
	return Z_Registration_Info_UEnum_EAURACRONPCGLODLevel.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONPCGLODLevel>()
{
	return EAURACRONPCGLODLevel_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * N\xc3\xadveis de LOD para elementos PCG\n */" },
#endif
		{ "LOD0_Highest.DisplayName", "LOD 0 - Highest Quality" },
		{ "LOD0_Highest.Name", "EAURACRONPCGLODLevel::LOD0_Highest" },
		{ "LOD1_High.DisplayName", "LOD 1 - High Quality" },
		{ "LOD1_High.Name", "EAURACRONPCGLODLevel::LOD1_High" },
		{ "LOD2_Medium.DisplayName", "LOD 2 - Medium Quality" },
		{ "LOD2_Medium.Name", "EAURACRONPCGLODLevel::LOD2_Medium" },
		{ "LOD3_Low.DisplayName", "LOD 3 - Low Quality" },
		{ "LOD3_Low.Name", "EAURACRONPCGLODLevel::LOD3_Low" },
		{ "LOD4_Culled.DisplayName", "LOD 4 - Culled" },
		{ "LOD4_Culled.Name", "EAURACRONPCGLODLevel::LOD4_Culled" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadveis de LOD para elementos PCG" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONPCGLODLevel::LOD0_Highest", (int64)EAURACRONPCGLODLevel::LOD0_Highest },
		{ "EAURACRONPCGLODLevel::LOD1_High", (int64)EAURACRONPCGLODLevel::LOD1_High },
		{ "EAURACRONPCGLODLevel::LOD2_Medium", (int64)EAURACRONPCGLODLevel::LOD2_Medium },
		{ "EAURACRONPCGLODLevel::LOD3_Low", (int64)EAURACRONPCGLODLevel::LOD3_Low },
		{ "EAURACRONPCGLODLevel::LOD4_Culled", (int64)EAURACRONPCGLODLevel::LOD4_Culled },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONPCGLODLevel",
	"EAURACRONPCGLODLevel",
	Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel()
{
	if (!Z_Registration_Info_UEnum_EAURACRONPCGLODLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONPCGLODLevel.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONPCGLODLevel.InnerSingleton;
}
// ********** End Enum EAURACRONPCGLODLevel ********************************************************

// ********** Begin ScriptStruct FAURACRONDevicePerformanceProfile *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONDevicePerformanceProfile;
class UScriptStruct* FAURACRONDevicePerformanceProfile::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONDevicePerformanceProfile.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONDevicePerformanceProfile.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONDevicePerformanceProfile"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONDevicePerformanceProfile.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas por tipo de dispositivo\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas por tipo de dispositivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeviceType_MetaData[] = {
		{ "Category", "Device" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do dispositivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityMultiplier_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de qualidade geral (0.0 - 2.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de qualidade geral (0.0 - 2.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRenderDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia m\xc3\xa1xima de renderiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xa1xima de renderiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxVisibleElements_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "50" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de elementos PCG vis\xc3\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de elementos PCG vis\xc3\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultLODLevel_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de LOD padr\xc3\xa3o para este dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de LOD padr\xc3\xa3o para este dispositivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAggressiveCulling_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar culling agressivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar culling agressivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlowVolatilityMultiplier_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de volatilidade do Prismal Flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de volatilidade do Prismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneIntersectionMultiplier_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de intersec\xc3\xa7\xc3\xa3o de trilhos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de intersec\xc3\xa7\xc3\xa3o de trilhos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DeviceType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DeviceType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRenderDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxVisibleElements;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultLODLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultLODLevel;
	static void NewProp_bUseAggressiveCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAggressiveCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PrismalFlowVolatilityMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LaneIntersectionMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONDevicePerformanceProfile>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_DeviceType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_DeviceType = { "DeviceType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONDevicePerformanceProfile, DeviceType), Z_Construct_UEnum_AURACRON_EAURACRONDeviceType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeviceType_MetaData), NewProp_DeviceType_MetaData) }; // 2389933137
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_QualityMultiplier = { "QualityMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONDevicePerformanceProfile, QualityMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityMultiplier_MetaData), NewProp_QualityMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_MaxRenderDistance = { "MaxRenderDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONDevicePerformanceProfile, MaxRenderDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRenderDistance_MetaData), NewProp_MaxRenderDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_MaxVisibleElements = { "MaxVisibleElements", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONDevicePerformanceProfile, MaxVisibleElements), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxVisibleElements_MetaData), NewProp_MaxVisibleElements_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_DefaultLODLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_DefaultLODLevel = { "DefaultLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONDevicePerformanceProfile, DefaultLODLevel), Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultLODLevel_MetaData), NewProp_DefaultLODLevel_MetaData) }; // 1031990892
void Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_bUseAggressiveCulling_SetBit(void* Obj)
{
	((FAURACRONDevicePerformanceProfile*)Obj)->bUseAggressiveCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_bUseAggressiveCulling = { "bUseAggressiveCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONDevicePerformanceProfile), &Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_bUseAggressiveCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAggressiveCulling_MetaData), NewProp_bUseAggressiveCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_PrismalFlowVolatilityMultiplier = { "PrismalFlowVolatilityMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONDevicePerformanceProfile, PrismalFlowVolatilityMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlowVolatilityMultiplier_MetaData), NewProp_PrismalFlowVolatilityMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_LaneIntersectionMultiplier = { "LaneIntersectionMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONDevicePerformanceProfile, LaneIntersectionMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneIntersectionMultiplier_MetaData), NewProp_LaneIntersectionMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_DeviceType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_DeviceType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_QualityMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_MaxRenderDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_MaxVisibleElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_DefaultLODLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_DefaultLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_bUseAggressiveCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_PrismalFlowVolatilityMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewProp_LaneIntersectionMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONDevicePerformanceProfile",
	Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::PropPointers),
	sizeof(FAURACRONDevicePerformanceProfile),
	alignof(FAURACRONDevicePerformanceProfile),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONDevicePerformanceProfile.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONDevicePerformanceProfile.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONDevicePerformanceProfile.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONDevicePerformanceProfile ***********************************

// ********** Begin ScriptStruct FAURACRONPCGPerformanceConfig *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceConfig;
class UScriptStruct* FAURACRONPCGPerformanceConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGPerformanceConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de performance para elementos PCG\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de performance para elementos PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncias para cada n\xc3\xadvel de LOD */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncias para cada n\xc3\xadvel de LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxVisibleElements_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de elementos vis\xc3\xadveis por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de elementos vis\xc3\xadveis por tipo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODUpdateInterval_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de atualiza\xc3\xa7\xc3\xa3o de LOD em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de atualiza\xc3\xa7\xc3\xa3o de LOD em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFrustumCulling_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar culling por frustum */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar culling por frustum" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseOcclusionCulling_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar occlusion culling */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar occlusion culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODDistance_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia m\xc3\xa1xima para LOD */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xa1xima para LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de culling */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOcclusion_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve habilitar occlusion culling */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve habilitar occlusion culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFrustumCulling_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve habilitar frustum culling */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve habilitar frustum culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRenderDistance_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia m\xc3\xa1xima de renderiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xa1xima de renderiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityScaleFactor_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceConfig" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fator de escala de qualidade baseado na performance */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fator de escala de qualidade baseado na performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxVisibleElements;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODUpdateInterval;
	static void NewProp_bUseFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFrustumCulling;
	static void NewProp_bUseOcclusionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseOcclusionCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxLODDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static void NewProp_bEnableOcclusion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOcclusion;
	static void NewProp_bEnableFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFrustumCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRenderDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityScaleFactor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGPerformanceConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceConfig, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_MaxVisibleElements = { "MaxVisibleElements", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceConfig, MaxVisibleElements), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxVisibleElements_MetaData), NewProp_MaxVisibleElements_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_LODUpdateInterval = { "LODUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceConfig, LODUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODUpdateInterval_MetaData), NewProp_LODUpdateInterval_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bUseFrustumCulling_SetBit(void* Obj)
{
	((FAURACRONPCGPerformanceConfig*)Obj)->bUseFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bUseFrustumCulling = { "bUseFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGPerformanceConfig), &Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bUseFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFrustumCulling_MetaData), NewProp_bUseFrustumCulling_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bUseOcclusionCulling_SetBit(void* Obj)
{
	((FAURACRONPCGPerformanceConfig*)Obj)->bUseOcclusionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bUseOcclusionCulling = { "bUseOcclusionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGPerformanceConfig), &Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bUseOcclusionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseOcclusionCulling_MetaData), NewProp_bUseOcclusionCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_MaxLODDistance = { "MaxLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceConfig, MaxLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODDistance_MetaData), NewProp_MaxLODDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceConfig, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bEnableOcclusion_SetBit(void* Obj)
{
	((FAURACRONPCGPerformanceConfig*)Obj)->bEnableOcclusion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bEnableOcclusion = { "bEnableOcclusion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGPerformanceConfig), &Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bEnableOcclusion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOcclusion_MetaData), NewProp_bEnableOcclusion_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bEnableFrustumCulling_SetBit(void* Obj)
{
	((FAURACRONPCGPerformanceConfig*)Obj)->bEnableFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bEnableFrustumCulling = { "bEnableFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGPerformanceConfig), &Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bEnableFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFrustumCulling_MetaData), NewProp_bEnableFrustumCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_MaxRenderDistance = { "MaxRenderDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceConfig, MaxRenderDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRenderDistance_MetaData), NewProp_MaxRenderDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_QualityScaleFactor = { "QualityScaleFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceConfig, QualityScaleFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityScaleFactor_MetaData), NewProp_QualityScaleFactor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_MaxVisibleElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_LODUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bUseFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bUseOcclusionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_MaxLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bEnableOcclusion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_bEnableFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_MaxRenderDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewProp_QualityScaleFactor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGPerformanceConfig",
	Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::PropPointers),
	sizeof(FAURACRONPCGPerformanceConfig),
	alignof(FAURACRONPCGPerformanceConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGPerformanceConfig ***************************************

// ********** Begin ScriptStruct FAURACRONPCGElementPerformanceInfo ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGElementPerformanceInfo;
class UScriptStruct* FAURACRONPCGElementPerformanceInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGElementPerformanceInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGElementPerformanceInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGElementPerformanceInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGElementPerformanceInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es de performance de um elemento PCG\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de performance de um elemento PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Actor_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancia ao ator */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia ao ator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLOD_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de LOD atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de LOD atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceToViewer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia do viewer */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia do viewer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInFrustum_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se est\xc3\xa1 vis\xc3\xadvel no frustum */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se est\xc3\xa1 vis\xc3\xadvel no frustum" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsOccluded_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se est\xc3\xa1 oclu\xc3\xad""do */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se est\xc3\xa1 oclu\xc3\xad""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderPriority_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de renderiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de renderiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x9altima vez que foi atualizado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x9altima vez que foi atualizado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGElement_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elemento PCG */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemento PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastFrameTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo do \xc3\xbaltimo frame */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo do \xc3\xbaltimo frame" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentLOD_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentLOD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceToViewer;
	static void NewProp_bIsInFrustum_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInFrustum;
	static void NewProp_bIsOccluded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOccluded;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RenderPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_PCGElement;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastFrameTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGElementPerformanceInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0014000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGElementPerformanceInfo, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Actor_MetaData), NewProp_Actor_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_CurrentLOD_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_CurrentLOD = { "CurrentLOD", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGElementPerformanceInfo, CurrentLOD), Z_Construct_UEnum_AURACRON_EAURACRONPCGLODLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLOD_MetaData), NewProp_CurrentLOD_MetaData) }; // 1031990892
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_DistanceToViewer = { "DistanceToViewer", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGElementPerformanceInfo, DistanceToViewer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceToViewer_MetaData), NewProp_DistanceToViewer_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_bIsInFrustum_SetBit(void* Obj)
{
	((FAURACRONPCGElementPerformanceInfo*)Obj)->bIsInFrustum = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_bIsInFrustum = { "bIsInFrustum", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGElementPerformanceInfo), &Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_bIsInFrustum_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInFrustum_MetaData), NewProp_bIsInFrustum_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_bIsOccluded_SetBit(void* Obj)
{
	((FAURACRONPCGElementPerformanceInfo*)Obj)->bIsOccluded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_bIsOccluded = { "bIsOccluded", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGElementPerformanceInfo), &Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_bIsOccluded_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsOccluded_MetaData), NewProp_bIsOccluded_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_RenderPriority = { "RenderPriority", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGElementPerformanceInfo, RenderPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderPriority_MetaData), NewProp_RenderPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGElementPerformanceInfo, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_PCGElement = { "PCGElement", nullptr, (EPropertyFlags)0x0014000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGElementPerformanceInfo, PCGElement), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGElement_MetaData), NewProp_PCGElement_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_LastFrameTime = { "LastFrameTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGElementPerformanceInfo, LastFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastFrameTime_MetaData), NewProp_LastFrameTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_CurrentLOD_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_CurrentLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_DistanceToViewer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_bIsInFrustum,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_bIsOccluded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_RenderPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_LastUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_PCGElement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewProp_LastFrameTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGElementPerformanceInfo",
	Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::PropPointers),
	sizeof(FAURACRONPCGElementPerformanceInfo),
	alignof(FAURACRONPCGElementPerformanceInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGElementPerformanceInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGElementPerformanceInfo.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGElementPerformanceInfo.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGElementPerformanceInfo **********************************

// ********** Begin ScriptStruct FAURACRONPCGPerformanceMetrics ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceMetrics;
class UScriptStruct* FAURACRONPCGPerformanceMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGPerformanceMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * M\xc3\xa9tricas de performance do sistema PCG\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\xa9tricas de performance do sistema PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFPS_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** FPS atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FPS atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTime_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de frame em ms */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de frame em ms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderedElements_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de elementos renderizados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de elementos renderizados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CulledElements_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de elementos culled */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de elementos culled" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUMemoryUsage_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Uso de mem\xc3\xb3ria de GPU em MB */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Uso de mem\xc3\xb3ria de GPU em MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de draw calls */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de draw calls" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingTime_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo gasto em culling em ms */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo gasto em culling em ms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFPS_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** FPS m\xc3\xa9""dio */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FPS m\xc3\xa9""dio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinFPS_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** FPS m\xc3\xadnimo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FPS m\xc3\xadnimo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFPS_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** FPS m\xc3\xa1ximo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FPS m\xc3\xa1ximo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UsedMemoryMB_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mem\xc3\xb3ria usada em MB */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mem\xc3\xb3ria usada em MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableMemoryMB_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mem\xc3\xb3ria dispon\xc3\xadvel em MB */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mem\xc3\xb3ria dispon\xc3\xadvel em MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderTargetWidth_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Largura do render target */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Largura do render target" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderTargetHeight_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura do render target */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura do render target" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivePCGElements_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elementos PCG ativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elementos PCG ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisiblePCGElements_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elementos PCG vis\xc3\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elementos PCG vis\xc3\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGMemoryUsageMB_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Uso de mem\xc3\xb3ria PCG em MB */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Uso de mem\xc3\xb3ria PCG em MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGEfficiencyRatio_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raz\xc3\xa3o de efici\xc3\xaancia PCG */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raz\xc3\xa3o de efici\xc3\xaancia PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameThreadTime_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo da thread do jogo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo da thread do jogo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedDrawCalls_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Draw calls estimados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Draw calls estimados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageElementDistance_MetaData[] = {
		{ "Category", "AURACRONPCGPerformanceMetrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia m\xc3\xa9""dia dos elementos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xa9""dia dos elementos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RenderedElements;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CulledElements;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUMemoryUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UsedMemoryMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AvailableMemoryMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RenderTargetWidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RenderTargetHeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActivePCGElements;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VisiblePCGElements;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PCGMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PCGEfficiencyRatio;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GameThreadTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EstimatedDrawCalls;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageElementDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGPerformanceMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_CurrentFPS = { "CurrentFPS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, CurrentFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFPS_MetaData), NewProp_CurrentFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_FrameTime = { "FrameTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, FrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTime_MetaData), NewProp_FrameTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_RenderedElements = { "RenderedElements", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, RenderedElements), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderedElements_MetaData), NewProp_RenderedElements_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_CulledElements = { "CulledElements", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, CulledElements), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CulledElements_MetaData), NewProp_CulledElements_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_GPUMemoryUsage = { "GPUMemoryUsage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, GPUMemoryUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUMemoryUsage_MetaData), NewProp_GPUMemoryUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_CullingTime = { "CullingTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, CullingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingTime_MetaData), NewProp_CullingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_AverageFPS = { "AverageFPS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, AverageFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFPS_MetaData), NewProp_AverageFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_MinFPS = { "MinFPS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, MinFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinFPS_MetaData), NewProp_MinFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_MaxFPS = { "MaxFPS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, MaxFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFPS_MetaData), NewProp_MaxFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_UsedMemoryMB = { "UsedMemoryMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, UsedMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UsedMemoryMB_MetaData), NewProp_UsedMemoryMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_AvailableMemoryMB = { "AvailableMemoryMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, AvailableMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableMemoryMB_MetaData), NewProp_AvailableMemoryMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_RenderTargetWidth = { "RenderTargetWidth", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, RenderTargetWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderTargetWidth_MetaData), NewProp_RenderTargetWidth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_RenderTargetHeight = { "RenderTargetHeight", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, RenderTargetHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderTargetHeight_MetaData), NewProp_RenderTargetHeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_ActivePCGElements = { "ActivePCGElements", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, ActivePCGElements), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivePCGElements_MetaData), NewProp_ActivePCGElements_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_VisiblePCGElements = { "VisiblePCGElements", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, VisiblePCGElements), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisiblePCGElements_MetaData), NewProp_VisiblePCGElements_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_PCGMemoryUsageMB = { "PCGMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, PCGMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGMemoryUsageMB_MetaData), NewProp_PCGMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_PCGEfficiencyRatio = { "PCGEfficiencyRatio", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, PCGEfficiencyRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGEfficiencyRatio_MetaData), NewProp_PCGEfficiencyRatio_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_GameThreadTime = { "GameThreadTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, GameThreadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameThreadTime_MetaData), NewProp_GameThreadTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_EstimatedDrawCalls = { "EstimatedDrawCalls", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, EstimatedDrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedDrawCalls_MetaData), NewProp_EstimatedDrawCalls_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_AverageElementDistance = { "AverageElementDistance", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGPerformanceMetrics, AverageElementDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageElementDistance_MetaData), NewProp_AverageElementDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_CurrentFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_FrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_RenderedElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_CulledElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_GPUMemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_CullingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_AverageFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_MinFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_MaxFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_UsedMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_AvailableMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_RenderTargetWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_RenderTargetHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_ActivePCGElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_VisiblePCGElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_PCGMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_PCGEfficiencyRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_GameThreadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_EstimatedDrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewProp_AverageElementDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGPerformanceMetrics",
	Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::PropPointers),
	sizeof(FAURACRONPCGPerformanceMetrics),
	alignof(FAURACRONPCGPerformanceMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceMetrics.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceMetrics.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGPerformanceMetrics **************************************

// ********** Begin Class AAURACRONPCGPerformanceManager Function ApplyDeviceSpecificSettings ******
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ApplyDeviceSpecificSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas do dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar configura\xc3\xa7\xc3\xb5""es espec\xc3\xad""ficas do dispositivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ApplyDeviceSpecificSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "ApplyDeviceSpecificSettings", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ApplyDeviceSpecificSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ApplyDeviceSpecificSettings_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ApplyDeviceSpecificSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ApplyDeviceSpecificSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execApplyDeviceSpecificSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyDeviceSpecificSettings();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function ApplyDeviceSpecificSettings ********

// ********** Begin Class AAURACRONPCGPerformanceManager Function DetectDeviceType *****************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics
{
	struct AURACRONPCGPerformanceManager_eventDetectDeviceType_Parms
	{
		EAURACRONDeviceType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detectar tipo de dispositivo atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detectar tipo de dispositivo atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventDetectDeviceType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONDeviceType, METADATA_PARAMS(0, nullptr) }; // 2389933137
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "DetectDeviceType", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::AURACRONPCGPerformanceManager_eventDetectDeviceType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::AURACRONPCGPerformanceManager_eventDetectDeviceType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execDetectDeviceType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONDeviceType*)Z_Param__Result=P_THIS->DetectDeviceType();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function DetectDeviceType *******************

// ********** Begin Class AAURACRONPCGPerformanceManager Function ForceUpdateLOD *******************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ForceUpdateLOD_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""ar atualiza\xc3\xa7\xc3\xa3o de LOD */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""ar atualiza\xc3\xa7\xc3\xa3o de LOD" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ForceUpdateLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "ForceUpdateLOD", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ForceUpdateLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ForceUpdateLOD_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ForceUpdateLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ForceUpdateLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execForceUpdateLOD)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceUpdateLOD();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function ForceUpdateLOD *********************

// ********** Begin Class AAURACRONPCGPerformanceManager Function GetCurrentDeviceProfile **********
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics
{
	struct AURACRONPCGPerformanceManager_eventGetCurrentDeviceProfile_Parms
	{
		FAURACRONDevicePerformanceProfile ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter perfil de performance do dispositivo atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter perfil de performance do dispositivo atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventGetCurrentDeviceProfile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile, METADATA_PARAMS(0, nullptr) }; // 1820843214
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "GetCurrentDeviceProfile", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::AURACRONPCGPerformanceManager_eventGetCurrentDeviceProfile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::AURACRONPCGPerformanceManager_eventGetCurrentDeviceProfile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execGetCurrentDeviceProfile)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONDevicePerformanceProfile*)Z_Param__Result=P_THIS->GetCurrentDeviceProfile();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function GetCurrentDeviceProfile ************

// ********** Begin Class AAURACRONPCGPerformanceManager Function GetDeviceBasedQualitySettings ****
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics
{
	struct AURACRONPCGPerformanceManager_eventGetDeviceBasedQualitySettings_Parms
	{
		float OutQualityMultiplier;
		int32 OutMaxElements;
		float OutMaxDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter configura\xc3\xa7\xc3\xb5""es de qualidade baseadas no dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\xa7\xc3\xb5""es de qualidade baseadas no dispositivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutQualityMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OutMaxElements;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutMaxDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::NewProp_OutQualityMultiplier = { "OutQualityMultiplier", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventGetDeviceBasedQualitySettings_Parms, OutQualityMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::NewProp_OutMaxElements = { "OutMaxElements", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventGetDeviceBasedQualitySettings_Parms, OutMaxElements), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::NewProp_OutMaxDistance = { "OutMaxDistance", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventGetDeviceBasedQualitySettings_Parms, OutMaxDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::NewProp_OutQualityMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::NewProp_OutMaxElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::NewProp_OutMaxDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "GetDeviceBasedQualitySettings", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::AURACRONPCGPerformanceManager_eventGetDeviceBasedQualitySettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::AURACRONPCGPerformanceManager_eventGetDeviceBasedQualitySettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execGetDeviceBasedQualitySettings)
{
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_OutQualityMultiplier);
	P_GET_PROPERTY_REF(FIntProperty,Z_Param_Out_OutMaxElements);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_OutMaxDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GetDeviceBasedQualitySettings(Z_Param_Out_OutQualityMultiplier,Z_Param_Out_OutMaxElements,Z_Param_Out_OutMaxDistance);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function GetDeviceBasedQualitySettings ******

// ********** Begin Class AAURACRONPCGPerformanceManager Function GetDeviceQualityMultiplier *******
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics
{
	struct AURACRONPCGPerformanceManager_eventGetDeviceQualityMultiplier_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter multiplicador de qualidade baseado no dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter multiplicador de qualidade baseado no dispositivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventGetDeviceQualityMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "GetDeviceQualityMultiplier", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::AURACRONPCGPerformanceManager_eventGetDeviceQualityMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::AURACRONPCGPerformanceManager_eventGetDeviceQualityMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execGetDeviceQualityMultiplier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDeviceQualityMultiplier();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function GetDeviceQualityMultiplier *********

// ********** Begin Class AAURACRONPCGPerformanceManager Function GetPerformanceMetrics ************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics
{
	struct AURACRONPCGPerformanceManager_eventGetPerformanceMetrics_Parms
	{
		FAURACRONPCGPerformanceMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter m\xc3\xa9tricas de performance */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter m\xc3\xa9tricas de performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventGetPerformanceMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 3129041930
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "GetPerformanceMetrics", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::AURACRONPCGPerformanceManager_eventGetPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::AURACRONPCGPerformanceManager_eventGetPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execGetPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAURACRONPCGPerformanceMetrics*)Z_Param__Result=P_THIS->GetPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function GetPerformanceMetrics **************

// ********** Begin Class AAURACRONPCGPerformanceManager Function InitializePerformanceSystem ******
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_InitializePerformanceSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar sistema de performance */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar sistema de performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_InitializePerformanceSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "InitializePerformanceSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_InitializePerformanceSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_InitializePerformanceSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_InitializePerformanceSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_InitializePerformanceSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execInitializePerformanceSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePerformanceSystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function InitializePerformanceSystem ********

// ********** Begin Class AAURACRONPCGPerformanceManager Function IsEntryDevice ********************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics
{
	struct AURACRONPCGPerformanceManager_eventIsEntryDevice_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se deve usar configura\xc3\xa7\xc3\xb5""es de Entry device */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se deve usar configura\xc3\xa7\xc3\xb5""es de Entry device" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPerformanceManager_eventIsEntryDevice_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPerformanceManager_eventIsEntryDevice_Parms), &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "IsEntryDevice", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::AURACRONPCGPerformanceManager_eventIsEntryDevice_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::AURACRONPCGPerformanceManager_eventIsEntryDevice_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execIsEntryDevice)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsEntryDevice();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function IsEntryDevice **********************

// ********** Begin Class AAURACRONPCGPerformanceManager Function IsHighDevice *********************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics
{
	struct AURACRONPCGPerformanceManager_eventIsHighDevice_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se deve usar configura\xc3\xa7\xc3\xb5""es de High device */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se deve usar configura\xc3\xa7\xc3\xb5""es de High device" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPerformanceManager_eventIsHighDevice_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPerformanceManager_eventIsHighDevice_Parms), &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "IsHighDevice", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::AURACRONPCGPerformanceManager_eventIsHighDevice_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::AURACRONPCGPerformanceManager_eventIsHighDevice_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execIsHighDevice)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsHighDevice();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function IsHighDevice ***********************

// ********** Begin Class AAURACRONPCGPerformanceManager Function IsMidDevice **********************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics
{
	struct AURACRONPCGPerformanceManager_eventIsMidDevice_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se deve usar configura\xc3\xa7\xc3\xb5""es de Mid device */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se deve usar configura\xc3\xa7\xc3\xb5""es de Mid device" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPerformanceManager_eventIsMidDevice_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPerformanceManager_eventIsMidDevice_Parms), &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "IsMidDevice", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::AURACRONPCGPerformanceManager_eventIsMidDevice_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::AURACRONPCGPerformanceManager_eventIsMidDevice_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execIsMidDevice)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsMidDevice();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function IsMidDevice ************************

// ********** Begin Class AAURACRONPCGPerformanceManager Function OnLODUpdateTimer *****************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnLODUpdateTimer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback para timer de LOD */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback para timer de LOD" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnLODUpdateTimer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "OnLODUpdateTimer", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnLODUpdateTimer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnLODUpdateTimer_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnLODUpdateTimer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnLODUpdateTimer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execOnLODUpdateTimer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnLODUpdateTimer();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function OnLODUpdateTimer *******************

// ********** Begin Class AAURACRONPCGPerformanceManager Function OnMetricsTimer *******************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnMetricsTimer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback para timer de m\xc3\xa9tricas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback para timer de m\xc3\xa9tricas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnMetricsTimer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "OnMetricsTimer", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnMetricsTimer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnMetricsTimer_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnMetricsTimer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnMetricsTimer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execOnMetricsTimer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnMetricsTimer();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function OnMetricsTimer *********************

// ********** Begin Class AAURACRONPCGPerformanceManager Function RegisterPCGElement ***************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics
{
	struct AURACRONPCGPerformanceManager_eventRegisterPCGElement_Parms
	{
		AActor* Element;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registrar elemento PCG para gerenciamento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar elemento PCG para gerenciamento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Element;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::NewProp_Element = { "Element", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventRegisterPCGElement_Parms, Element), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::NewProp_Element,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "RegisterPCGElement", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::AURACRONPCGPerformanceManager_eventRegisterPCGElement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::AURACRONPCGPerformanceManager_eventRegisterPCGElement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execRegisterPCGElement)
{
	P_GET_OBJECT(AActor,Z_Param_Element);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterPCGElement(Z_Param_Element);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function RegisterPCGElement *****************

// ********** Begin Class AAURACRONPCGPerformanceManager Function SetAutoOptimizationEnabled *******
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics
{
	struct AURACRONPCGPerformanceManager_eventSetAutoOptimizationEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar/desativar otimiza\xc3\xa7\xc3\xb5""es autom\xc3\xa1ticas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar/desativar otimiza\xc3\xa7\xc3\xb5""es autom\xc3\xa1ticas" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AURACRONPCGPerformanceManager_eventSetAutoOptimizationEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPerformanceManager_eventSetAutoOptimizationEnabled_Parms), &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "SetAutoOptimizationEnabled", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::AURACRONPCGPerformanceManager_eventSetAutoOptimizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::AURACRONPCGPerformanceManager_eventSetAutoOptimizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execSetAutoOptimizationEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAutoOptimizationEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function SetAutoOptimizationEnabled *********

// ********** Begin Class AAURACRONPCGPerformanceManager Function SetPerformanceConfig *************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics
{
	struct AURACRONPCGPerformanceManager_eventSetPerformanceConfig_Parms
	{
		FAURACRONPCGPerformanceConfig NewConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir configura\xc3\xa7\xc3\xb5""es de performance */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir configura\xc3\xa7\xc3\xb5""es de performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventSetPerformanceConfig_Parms, NewConfig), Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 2010874092
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::NewProp_NewConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "SetPerformanceConfig", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::AURACRONPCGPerformanceManager_eventSetPerformanceConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::AURACRONPCGPerformanceManager_eventSetPerformanceConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execSetPerformanceConfig)
{
	P_GET_STRUCT_REF(FAURACRONPCGPerformanceConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPerformanceConfig(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function SetPerformanceConfig ***************

// ********** Begin Class AAURACRONPCGPerformanceManager Function UnregisterPCGElement *************
struct Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics
{
	struct AURACRONPCGPerformanceManager_eventUnregisterPCGElement_Parms
	{
		AActor* Element;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desregistrar elemento PCG */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desregistrar elemento PCG" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Element;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::NewProp_Element = { "Element", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPerformanceManager_eventUnregisterPCGElement_Parms, Element), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::NewProp_Element,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPerformanceManager, nullptr, "UnregisterPCGElement", Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::AURACRONPCGPerformanceManager_eventUnregisterPCGElement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::AURACRONPCGPerformanceManager_eventUnregisterPCGElement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPerformanceManager::execUnregisterPCGElement)
{
	P_GET_OBJECT(AActor,Z_Param_Element);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterPCGElement(Z_Param_Element);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPerformanceManager Function UnregisterPCGElement ***************

// ********** Begin Class AAURACRONPCGPerformanceManager *******************************************
void AAURACRONPCGPerformanceManager::StaticRegisterNativesAAURACRONPCGPerformanceManager()
{
	UClass* Class = AAURACRONPCGPerformanceManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyDeviceSpecificSettings", &AAURACRONPCGPerformanceManager::execApplyDeviceSpecificSettings },
		{ "DetectDeviceType", &AAURACRONPCGPerformanceManager::execDetectDeviceType },
		{ "ForceUpdateLOD", &AAURACRONPCGPerformanceManager::execForceUpdateLOD },
		{ "GetCurrentDeviceProfile", &AAURACRONPCGPerformanceManager::execGetCurrentDeviceProfile },
		{ "GetDeviceBasedQualitySettings", &AAURACRONPCGPerformanceManager::execGetDeviceBasedQualitySettings },
		{ "GetDeviceQualityMultiplier", &AAURACRONPCGPerformanceManager::execGetDeviceQualityMultiplier },
		{ "GetPerformanceMetrics", &AAURACRONPCGPerformanceManager::execGetPerformanceMetrics },
		{ "InitializePerformanceSystem", &AAURACRONPCGPerformanceManager::execInitializePerformanceSystem },
		{ "IsEntryDevice", &AAURACRONPCGPerformanceManager::execIsEntryDevice },
		{ "IsHighDevice", &AAURACRONPCGPerformanceManager::execIsHighDevice },
		{ "IsMidDevice", &AAURACRONPCGPerformanceManager::execIsMidDevice },
		{ "OnLODUpdateTimer", &AAURACRONPCGPerformanceManager::execOnLODUpdateTimer },
		{ "OnMetricsTimer", &AAURACRONPCGPerformanceManager::execOnMetricsTimer },
		{ "RegisterPCGElement", &AAURACRONPCGPerformanceManager::execRegisterPCGElement },
		{ "SetAutoOptimizationEnabled", &AAURACRONPCGPerformanceManager::execSetAutoOptimizationEnabled },
		{ "SetPerformanceConfig", &AAURACRONPCGPerformanceManager::execSetPerformanceConfig },
		{ "UnregisterPCGElement", &AAURACRONPCGPerformanceManager::execUnregisterPCGElement },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGPerformanceManager;
UClass* AAURACRONPCGPerformanceManager::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGPerformanceManager;
	if (!Z_Registration_Info_UClass_AAURACRONPCGPerformanceManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGPerformanceManager"),
			Z_Registration_Info_UClass_AAURACRONPCGPerformanceManager.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGPerformanceManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPerformanceManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGPerformanceManager_NoRegister()
{
	return AAURACRONPCGPerformanceManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Gerenciador de performance para sistema PCG AURACRON\n * Implementa LOD din\xc3\xa2mico, culling inteligente e streaming de assets usando UE 5.6\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGPerformanceManager.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciador de performance para sistema PCG AURACRON\nImplementa LOD din\xc3\xa2mico, culling inteligente e streaming de assets usando UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceConfig_MetaData[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de performance */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoOptimizationEnabled_MetaData[] = {
		{ "Category", "AURACRON|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se otimiza\xc3\xa7\xc3\xb5""es autom\xc3\xa1ticas est\xc3\xa3o ativas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se otimiza\xc3\xa7\xc3\xb5""es autom\xc3\xa1ticas est\xc3\xa3o ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetFPS_MetaData[] = {
		{ "Category", "AURACRON|Performance" },
		{ "ClampMax", "120.0" },
		{ "ClampMin", "30.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** FPS alvo para otimiza\xc3\xa7\xc3\xb5""es autom\xc3\xa1ticas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "FPS alvo para otimiza\xc3\xa7\xc3\xb5""es autom\xc3\xa1ticas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FPSTolerance_MetaData[] = {
		{ "Category", "AURACRON|Performance" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Margem de toler\xc3\xa2ncia para FPS */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Margem de toler\xc3\xa2ncia para FPS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredElements_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elementos PCG registrados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elementos PCG registrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementPerformanceInfo_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mapa de informa\xc3\xa7\xc3\xb5""es de performance por elemento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mapa de informa\xc3\xa7\xc3\xb5""es de performance por elemento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMetrics_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\xa9tricas atuais de performance */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\xa9tricas atuais de performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODUpdateTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para atualiza\xc3\xa7\xc3\xa3o de LOD */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para atualiza\xc3\xa7\xc3\xa3o de LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricsTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para coleta de m\xc3\xa9tricas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para coleta de m\xc3\xa9tricas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocations_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cache de viewers para culling */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache de viewers para culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FPSHistory_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Hist\xc3\xb3rico de FPS para suaviza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Hist\xc3\xb3rico de FPS para suaviza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentDeviceType_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de dispositivo atual detectado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de dispositivo atual detectado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentDeviceProfile_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Perfil de performance do dispositivo atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perfil de performance do dispositivo atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeviceProfiles_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Perfis pr\xc3\xa9-configurados para cada tipo de dispositivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perfis pr\xc3\xa9-configurados para cada tipo de dispositivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDeviceDetected_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a detec\xc3\xa7\xc3\xa3o de dispositivo j\xc3\xa1 foi executada */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPerformanceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a detec\xc3\xa7\xc3\xa3o de dispositivo j\xc3\xa1 foi executada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceConfig;
	static void NewProp_bAutoOptimizationEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoOptimizationEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FPSTolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredElements_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RegisteredElements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElementPerformanceInfo_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElementPerformanceInfo_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ElementPerformanceInfo;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LODUpdateTimer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MetricsTimer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ViewerLocations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FPSHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FPSHistory;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentDeviceType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentDeviceType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentDeviceProfile;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DeviceProfiles_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DeviceProfiles_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DeviceProfiles_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_DeviceProfiles;
	static void NewProp_bDeviceDetected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDeviceDetected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ApplyDeviceSpecificSettings, "ApplyDeviceSpecificSettings" }, // 1159658874
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_DetectDeviceType, "DetectDeviceType" }, // 4134621124
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_ForceUpdateLOD, "ForceUpdateLOD" }, // 305570932
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetCurrentDeviceProfile, "GetCurrentDeviceProfile" }, // 1064176344
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceBasedQualitySettings, "GetDeviceBasedQualitySettings" }, // 2709164634
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetDeviceQualityMultiplier, "GetDeviceQualityMultiplier" }, // 883250498
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_GetPerformanceMetrics, "GetPerformanceMetrics" }, // 861529357
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_InitializePerformanceSystem, "InitializePerformanceSystem" }, // 2336779145
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsEntryDevice, "IsEntryDevice" }, // 2201290799
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsHighDevice, "IsHighDevice" }, // 3929865781
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_IsMidDevice, "IsMidDevice" }, // 1835103681
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnLODUpdateTimer, "OnLODUpdateTimer" }, // 2740757710
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_OnMetricsTimer, "OnMetricsTimer" }, // 584843724
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_RegisterPCGElement, "RegisterPCGElement" }, // 771637539
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetAutoOptimizationEnabled, "SetAutoOptimizationEnabled" }, // 2973778129
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_SetPerformanceConfig, "SetPerformanceConfig" }, // 1658197896
		{ &Z_Construct_UFunction_AAURACRONPCGPerformanceManager_UnregisterPCGElement, "UnregisterPCGElement" }, // 1030990162
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGPerformanceManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_PerformanceConfig = { "PerformanceConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, PerformanceConfig), Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceConfig_MetaData), NewProp_PerformanceConfig_MetaData) }; // 2010874092
void Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_bAutoOptimizationEnabled_SetBit(void* Obj)
{
	((AAURACRONPCGPerformanceManager*)Obj)->bAutoOptimizationEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_bAutoOptimizationEnabled = { "bAutoOptimizationEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGPerformanceManager), &Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_bAutoOptimizationEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoOptimizationEnabled_MetaData), NewProp_bAutoOptimizationEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_TargetFPS = { "TargetFPS", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, TargetFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetFPS_MetaData), NewProp_TargetFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_FPSTolerance = { "FPSTolerance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, FPSTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FPSTolerance_MetaData), NewProp_FPSTolerance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_RegisteredElements_Inner = { "RegisteredElements", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo, METADATA_PARAMS(0, nullptr) }; // 3245933805
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_RegisteredElements = { "RegisteredElements", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, RegisteredElements), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredElements_MetaData), NewProp_RegisteredElements_MetaData) }; // 3245933805
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ElementPerformanceInfo_ValueProp = { "ElementPerformanceInfo", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo, METADATA_PARAMS(0, nullptr) }; // 3245933805
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ElementPerformanceInfo_Key_KeyProp = { "ElementPerformanceInfo_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ElementPerformanceInfo = { "ElementPerformanceInfo", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, ElementPerformanceInfo), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementPerformanceInfo_MetaData), NewProp_ElementPerformanceInfo_MetaData) }; // 3245933805
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_CurrentMetrics = { "CurrentMetrics", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, CurrentMetrics), Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMetrics_MetaData), NewProp_CurrentMetrics_MetaData) }; // 3129041930
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_LODUpdateTimer = { "LODUpdateTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, LODUpdateTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODUpdateTimer_MetaData), NewProp_LODUpdateTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_MetricsTimer = { "MetricsTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, MetricsTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricsTimer_MetaData), NewProp_MetricsTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ViewerLocations_Inner = { "ViewerLocations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ViewerLocations = { "ViewerLocations", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, ViewerLocations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocations_MetaData), NewProp_ViewerLocations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_FPSHistory_Inner = { "FPSHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_FPSHistory = { "FPSHistory", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, FPSHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FPSHistory_MetaData), NewProp_FPSHistory_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_CurrentDeviceType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_CurrentDeviceType = { "CurrentDeviceType", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, CurrentDeviceType), Z_Construct_UEnum_AURACRON_EAURACRONDeviceType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentDeviceType_MetaData), NewProp_CurrentDeviceType_MetaData) }; // 2389933137
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_CurrentDeviceProfile = { "CurrentDeviceProfile", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, CurrentDeviceProfile), Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentDeviceProfile_MetaData), NewProp_CurrentDeviceProfile_MetaData) }; // 1820843214
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_DeviceProfiles_ValueProp = { "DeviceProfiles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile, METADATA_PARAMS(0, nullptr) }; // 1820843214
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_DeviceProfiles_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_DeviceProfiles_Key_KeyProp = { "DeviceProfiles_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONDeviceType, METADATA_PARAMS(0, nullptr) }; // 2389933137
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_DeviceProfiles = { "DeviceProfiles", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPerformanceManager, DeviceProfiles), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeviceProfiles_MetaData), NewProp_DeviceProfiles_MetaData) }; // 2389933137 1820843214
void Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_bDeviceDetected_SetBit(void* Obj)
{
	((AAURACRONPCGPerformanceManager*)Obj)->bDeviceDetected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_bDeviceDetected = { "bDeviceDetected", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGPerformanceManager), &Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_bDeviceDetected_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDeviceDetected_MetaData), NewProp_bDeviceDetected_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_PerformanceConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_bAutoOptimizationEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_TargetFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_FPSTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_RegisteredElements_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_RegisteredElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ElementPerformanceInfo_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ElementPerformanceInfo_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ElementPerformanceInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_CurrentMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_LODUpdateTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_MetricsTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ViewerLocations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_ViewerLocations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_FPSHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_FPSHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_CurrentDeviceType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_CurrentDeviceType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_CurrentDeviceProfile,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_DeviceProfiles_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_DeviceProfiles_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_DeviceProfiles_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_DeviceProfiles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::NewProp_bDeviceDetected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::ClassParams = {
	&AAURACRONPCGPerformanceManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGPerformanceManager()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGPerformanceManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGPerformanceManager.OuterSingleton, Z_Construct_UClass_AAURACRONPCGPerformanceManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPerformanceManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGPerformanceManager);
AAURACRONPCGPerformanceManager::~AAURACRONPCGPerformanceManager() {}
// ********** End Class AAURACRONPCGPerformanceManager *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONPCGLODLevel_StaticEnum, TEXT("EAURACRONPCGLODLevel"), &Z_Registration_Info_UEnum_EAURACRONPCGLODLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1031990892U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONDevicePerformanceProfile::StaticStruct, Z_Construct_UScriptStruct_FAURACRONDevicePerformanceProfile_Statics::NewStructOps, TEXT("AURACRONDevicePerformanceProfile"), &Z_Registration_Info_UScriptStruct_FAURACRONDevicePerformanceProfile, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONDevicePerformanceProfile), 1820843214U) },
		{ FAURACRONPCGPerformanceConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGPerformanceConfig_Statics::NewStructOps, TEXT("AURACRONPCGPerformanceConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGPerformanceConfig), 2010874092U) },
		{ FAURACRONPCGElementPerformanceInfo::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGElementPerformanceInfo_Statics::NewStructOps, TEXT("AURACRONPCGElementPerformanceInfo"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGElementPerformanceInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGElementPerformanceInfo), 3245933805U) },
		{ FAURACRONPCGPerformanceMetrics::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGPerformanceMetrics_Statics::NewStructOps, TEXT("AURACRONPCGPerformanceMetrics"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGPerformanceMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGPerformanceMetrics), 3129041930U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGPerformanceManager, AAURACRONPCGPerformanceManager::StaticClass, TEXT("AAURACRONPCGPerformanceManager"), &Z_Registration_Info_UClass_AAURACRONPCGPerformanceManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGPerformanceManager), 3068791641U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h__Script_AURACRON_3886260186(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPerformanceManager_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
