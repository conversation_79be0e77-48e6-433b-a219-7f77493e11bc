// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGIsland.h"

#ifdef AURACRON_AURACRONPCGIsland_generated_h
#error "AURACRONPCGIsland.generated.h already included, missing '#pragma once' in AURACRONPCGIsland.h"
#endif
#define AURACRON_AURACRONPCGIsland_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPCGComponent;
enum class EAURACRONIslandType : uint8;
enum class EAURACRONMapPhase : uint8;

// ********** Begin Class AAURACRONPCGIsland *******************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h_26_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetPCGComponent); \
	DECLARE_FUNCTION(execSetIslandHeight); \
	DECLARE_FUNCTION(execGetIslandSize); \
	DECLARE_FUNCTION(execSetIslandSize); \
	DECLARE_FUNCTION(execSetIslandPosition); \
	DECLARE_FUNCTION(execSetFullyEmerged); \
	DECLARE_FUNCTION(execConfigureForConvergencePhase); \
	DECLARE_FUNCTION(execConfigureForAwakeningPhase); \
	DECLARE_FUNCTION(execSetActivityScale); \
	DECLARE_FUNCTION(execSetIslandVisibility); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGenerateIsland); \
	DECLARE_FUNCTION(execGetIslandType); \
	DECLARE_FUNCTION(execSetIslandType);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGIsland_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h_26_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGIsland(); \
	friend struct Z_Construct_UClass_AAURACRONPCGIsland_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGIsland_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGIsland, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGIsland_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGIsland)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h_26_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGIsland(AAURACRONPCGIsland&&) = delete; \
	AAURACRONPCGIsland(const AAURACRONPCGIsland&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGIsland); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGIsland); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGIsland) \
	NO_API virtual ~AAURACRONPCGIsland();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h_23_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h_26_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h_26_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h_26_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h_26_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGIsland;

// ********** End Class AAURACRONPCGIsland *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGIsland_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
