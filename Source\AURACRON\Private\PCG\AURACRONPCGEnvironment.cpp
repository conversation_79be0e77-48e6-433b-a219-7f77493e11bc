// AURACRONPCGEnvironment.cpp
// Sistema de GeraÃ§Ã£o Procedural para AURACRON - UE 5.6
// ImplementaÃ§Ã£o da classe para gerenciar os ambientes procedurais

#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSettings.h"
#include "PCGVolume.h"
#include "Helpers/PCGGraphParameterExtension.h"
#include "StructUtils/PropertyBag.h"
#include "Engine/World.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "PCG/AURACRONPCGPortal.h"
#include "PCG/AURACRONPCGPhaseManager.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "UObject/ConstructorHelpers.h"

AAURACRONPCGEnvironment::AAURACRONPCGEnvironment()
{
    PrimaryActorTick.bCanEverTick = true;

    // Criar o componente raiz (USceneComponent)
    USceneComponent* SceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("SceneRoot"));
    RootComponent = SceneRoot;

    // Criar o componente PCG
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));

    // Criar o componente de Post Process para efeitos de boundary
    BoundaryPostProcessComponent = CreateDefaultSubobject<UPostProcessComponent>(TEXT("BoundaryPostProcessComponent"));
    BoundaryPostProcessComponent->SetupAttachment(RootComponent);

    // Valores padrÃ£o
    EnvironmentType = EAURACRONEnvironmentType::RadiantPlains;
    ActivityScale = 0.0f;

    // Inicializar propriedades de qualidade de renderização
    bIsHighEndDevice = true; // Padrão high-end
    MaxParticleCount = 3000; // Padrão médio
    LightingQuality = 2; // Padrão médio
    ShadowQuality = 2; // Padrão médio
    EffectQuality = 2; // Padrão médio

    // Inicializar propriedades de ambiente
    EnvironmentIntensity = 1.0f;
    TransitionSpeed = 1.0f;
    BlendRadius = 1000.0f;
    EmergenceRate = 1.0f;
    bIsEmergingGradually = false;
    EmergenceStartTime = 0.0f;

    // Inicializar propriedades de phase manager e blending
    PhaseManagerReference = nullptr;
    bEnvironmentBlendingEnabled = false;
    BlendingStrength = 1.0f;

    // Inicializar propriedades de transições e interação
    bAdvancedTransitionsEnabled = false;
    TransitionComplexity = 1.0f;
    bInteractiveElementsEnabled = false;
    InteractionRadius = 1000.0f;

    // Inicializar propriedades de efeitos especiais
    bLightBlendingEnabled = false;
    bWindTransitionsEnabled = false;
    bSpectralInteractionsEnabled = false;
    bSmoothTransitionsEnabled = false;

    // Inicializar propriedades de altitude e transições
    AltitudeEffect = 0.0f;
    TransitionGradient = 1.0f;
    bHeightBasedTransitionEnabled = false;
    ParticleTransitionRate = 1.0f;

    // Inicializar propriedades de nuvens e Zephyr
    bVolumetricCloudsEnabled = false;
    CloudTransitionSpeed = 1.0f;
    bTransitionToZephyr = false;

    // Inicializar propriedades de modo simultâneo e blending cruzado
    ZephyrTransitionStrength = 1.0f;
    bSimultaneousModeEnabled = false;
    EnvironmentPriority = 5; // Prioridade média
    bCrossEnvironmentBlendingEnabled = false;
    CrossBlendStrength = 1.0f;

    // Inicializar caracterÃ­sticas especÃ­ficas
    bHasCrystallinePlateaus = true;
    bHasLivingCanyons = true;
    bHasBreathingForests = true;
    bHasTectonicBridges = true;
    
    bHasOrbitalArchipelagos = true;
    bHasAuroraBridges = true;
    bHasCloudFortresses = true;
    bHasStellarGardens = true;
    bHasVoidRifts = true;
    
    bHasSpectralPlains = true;
    bHasRiversOfSouls = true;
    bHasFragmentedStructures = true;
    bHasTemporalDistortionZones = true;
    bHasShadowStructures = true;
}

void AAURACRONPCGEnvironment::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar o componente PCG com as configuraÃ§Ãµes apropriadas (UE 5.6 API moderna)
    if (EnvironmentSettings)
    {
        // No UE 5.6, as configuraÃ§Ãµes sÃ£o definidas via GraphInstance
        if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironment: EnvironmentSettings configurado via GraphInstance"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGEnvironment: GraphInstance nÃ£o encontrado - configuraÃ§Ãµes serÃ£o definidas via Blueprint"));
        }
    }
    
    // Iniciar com visibilidade desativada atÃ© que seja explicitamente ativado
    SetEnvironmentVisibility(false);
    
    // Definir escala de atividade inicial
    SetActivityScale(0.0f);
}

void AAURACRONPCGEnvironment::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Atualizar elementos dinâmicos do ambiente
    if (bIsActive)
    {
        UpdateDynamicElements(DeltaTime);
    }
}

void AAURACRONPCGEnvironment::SetEnvironmentType(EAURACRONEnvironmentType NewType)
{
    EnvironmentType = NewType;
    
    // Reconfigurar o ambiente com base no novo tipo
    // Isso pode envolver a alteraÃ§Ã£o das configuraÃ§Ãµes do PCG
    GenerateEnvironment();
}

void AAURACRONPCGEnvironment::GenerateEnvironment()
{
    // Verificar se temos configuraÃ§Ãµes vÃ¡lidas
    if (!EnvironmentSettings)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGEnvironment: Nenhuma configuraÃ§Ã£o PCG definida para o ambiente %s"), 
               *GetNameSafe(this));
        return;
    }

    // Gerar caracterÃ­sticas especÃ­ficas com base no tipo de ambiente
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        if (bHasCrystallinePlateaus) GenerateCrystallinePlateaus();
        if (bHasLivingCanyons) GenerateLivingCanyons();
        if (bHasBreathingForests) GenerateBreathingForests();
        if (bHasTectonicBridges) GenerateTectonicBridges();
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        if (bHasOrbitalArchipelagos) GenerateOrbitalArchipelagos();
        if (bHasAuroraBridges) GenerateAuroraBridges();
        if (bHasCloudFortresses) GenerateCloudFortresses();
        if (bHasStellarGardens) GenerateStellarGardens();
        if (bHasVoidRifts) GenerateVoidRifts();
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        if (bHasSpectralPlains) GenerateSpectralPlains();
        if (bHasRiversOfSouls) GenerateRiversOfSouls();
        if (bHasFragmentedStructures) GenerateFragmentedStructures();
        if (bHasTemporalDistortionZones) GenerateTemporalDistortionZones();
        if (bHasShadowStructures) {
            GenerateShadowNexuses();
            GenerateTowersOfLamentation();
            GenerateSpectralGuardian();
            GeneratePurgatoryAnchor();
        }
        break;
    }

    // Executar a geraÃ§Ã£o PCG
    PCGComponent->Generate();
}

void AAURACRONPCGEnvironment::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Ajustar o ambiente com base na fase do mapa
    // Isso pode envolver a alteraÃ§Ã£o de parÃ¢metros de geraÃ§Ã£o ou a ativaÃ§Ã£o/desativaÃ§Ã£o de caracterÃ­sticas
    
    // Exemplo: Aumentar a intensidade de certas caracterÃ­sticas durante a fase de Prismal Flow
    if (MapPhase == EAURACRONMapPhase::Intensification)
    {
        // Aumentar a intensidade de caracterÃ­sticas especÃ­ficas
        // Isso seria implementado atravÃ©s de parÃ¢metros especÃ­ficos no PCG
    }
    
    // Regenerar o ambiente com as novas configuraÃ§Ãµes
    GenerateEnvironment();
}

void AAURACRONPCGEnvironment::SetEnvironmentVisibility(bool bVisible)
{
    // Definir a visibilidade de todos os componentes gerados
    SetActorHiddenInGame(!bVisible);
    
    // Se estiver visÃ­vel, garantir que a geraÃ§Ã£o PCG esteja atualizada
    if (bVisible)
    {
        GenerateEnvironment();
    }
}

void AAURACRONPCGEnvironment::SetActivityScale(float Scale)
{
    // Limitar a escala entre 0.0 e 1.0
    ActivityScale = FMath::Clamp(Scale, 0.0f, 1.0f);
    
    // Aplicar a escala de atividade aos parÃ¢metros de geraÃ§Ã£o
    // Isso pode envolver a alteraÃ§Ã£o de parÃ¢metros especÃ­ficos no PCG
    
    // Regenerar o ambiente se a escala for significativa
    if (ActivityScale > 0.01f)
    {
        GenerateEnvironment();
    }
}

// ========================================
// IMPLEMENTAÃ‡Ã•ES DAS FUNÃ‡Ã•ES DE GERAÃ‡ÃƒO ESPECÃFICAS
// ========================================

void AAURACRONPCGEnvironment::GenerateCrystallinePlateaus()
{
    if (!HasAuthority())
    {
        return;
    }

    // Usar as dimensÃµes definidas no sistema de medidas
    int32 PlateauCount = FMath::RandRange(
        FAURACRONMapDimensions::CRYSTALLINE_PLATEAUS_COUNT_MIN,
        FAURACRONMapDimensions::CRYSTALLINE_PLATEAUS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    // Distribuir platÃ´s usando amostragem de Poisson
    TArray<FVector> PlateauPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        EnvironmentCenter,
        EnvironmentRadius * 0.8f, // Dentro de 80% do raio do ambiente
        800.0f, // DistÃ¢ncia mÃ­nima entre platÃ´s (8 metros)
        30,
        12345
    );

    // Limitar ao nÃºmero desejado
    int32 ActualCount = FMath::Min(PlateauCount, PlateauPositions.Num());

    for (int32 i = 0; i < ActualCount; ++i)
    {
        FVector PlateauCenter = PlateauPositions[i];
        float PlateauRadius = FMath::RandRange(300.0f, 600.0f); // 3-6 metros
        float PlateauHeight = FMath::RandRange(
            FAURACRONMapDimensions::CRYSTALLINE_PLATEAU_HEIGHT_MIN_CM,
            FAURACRONMapDimensions::CRYSTALLINE_PLATEAU_HEIGHT_MAX_CM
        );

        // Gerar vÃ©rtices do platÃ´
        TArray<FVector> PlateauVertices = UAURACRONPCGMathLibrary::GenerateCrystallinePlateauVertices(
            PlateauCenter,
            PlateauRadius,
            PlateauHeight,
            8, // 8 lados
            0.3f // 30% de irregularidade
        );

        // Criar ator para o platÃ´ (seria substituÃ­do por geraÃ§Ã£o PCG real)
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = this;

        AStaticMeshActor* PlateauActor = GetWorld()->SpawnActor<AStaticMeshActor>(
            PlateauCenter, FRotator::ZeroRotator, SpawnParams
        );

        if (PlateauActor)
        {
            GeneratedActors.Add(PlateauActor);
        }
    }
}

void AAURACRONPCGEnvironment::GenerateLivingCanyons()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 CanyonCount = FMath::RandRange(
        FAURACRONMapDimensions::LIVING_CANYONS_COUNT_MIN,
        FAURACRONMapDimensions::LIVING_CANYONS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < CanyonCount; ++i)
    {
        // Gerar pontos de inÃ­cio e fim aleatÃ³rios
        float StartAngle = FMath::RandRange(0.0f, 2.0f * PI);
        float EndAngle = StartAngle + FMath::RandRange(PI * 0.5f, PI * 1.5f);

        FVector StartPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.6f * FMath::Cos(StartAngle),
            EnvironmentRadius * 0.6f * FMath::Sin(StartAngle),
            0.0f
        );

        FVector EndPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.6f * FMath::Cos(EndAngle),
            EnvironmentRadius * 0.6f * FMath::Sin(EndAngle),
            0.0f
        );

        float CanyonWidth = FMath::RandRange(400.0f, 800.0f); // 4-8 metros
        float CanyonDepth = FMath::RandRange(
            FAURACRONMapDimensions::LIVING_CANYON_DEPTH_MIN_CM,
            FAURACRONMapDimensions::LIVING_CANYON_DEPTH_MAX_CM
        );

        // Gerar caminho do cÃ¢nion
        TArray<FVector> CanyonPath = UAURACRONPCGMathLibrary::GenerateLivingCanyonPath(
            StartPoint,
            EndPoint,
            CanyonWidth,
            CanyonDepth,
            20 // 20 segmentos
        );

        // Criar representaÃ§Ã£o do cÃ¢nion (seria substituÃ­do por geraÃ§Ã£o PCG real)
        for (const FVector& PathPoint : CanyonPath)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;

            AStaticMeshActor* CanyonSegment = GetWorld()->SpawnActor<AStaticMeshActor>(
                PathPoint, FRotator::ZeroRotator, SpawnParams
            );

            if (CanyonSegment)
            {
                GeneratedActors.Add(CanyonSegment);
            }
        }
    }
}

void AAURACRONPCGEnvironment::GenerateBreathingForests()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 ForestCount = FMath::RandRange(
        FAURACRONMapDimensions::BREATHING_FORESTS_COUNT_MIN,
        FAURACRONMapDimensions::BREATHING_FORESTS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    // Distribuir centros das florestas
    TArray<FVector> ForestCenters = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        EnvironmentCenter,
        EnvironmentRadius * 0.7f,
        1000.0f, // DistÃ¢ncia mÃ­nima entre florestas (10 metros)
        30,
        54321
    );

    int32 ActualForestCount = FMath::Min(ForestCount, ForestCenters.Num());

    for (int32 i = 0; i < ActualForestCount; ++i)
    {
        FVector ForestCenter = ForestCenters[i];
        float ForestRadius = FMath::RandRange(
            FAURACRONMapDimensions::BREATHING_FOREST_RADIUS_MIN_CM,
            FAURACRONMapDimensions::BREATHING_FOREST_RADIUS_MAX_CM
        );

        int32 TreeCount = FMath::RandRange(20, 50);

        // Gerar posiÃ§Ãµes das Ã¡rvores
        TArray<FVector> TreePositions = UAURACRONPCGMathLibrary::GenerateBreathingForestPositions(
            ForestCenter,
            ForestRadius,
            TreeCount,
            150.0f, // DistÃ¢ncia mÃ­nima entre Ã¡rvores (1.5 metros)
            30.0f,  // Amplitude de respiraÃ§Ã£o (30cm)
            0.0f    // Tempo inicial
        );

        // Armazenar informaÃ§Ãµes da floresta para atualizaÃ§Ã£o dinÃ¢mica
        BreathingForests.Add({ForestCenter, ForestRadius, TreePositions});

        // Criar Ã¡rvores
        for (const FVector& TreePos : TreePositions)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;

            AStaticMeshActor* Tree = GetWorld()->SpawnActor<AStaticMeshActor>(
                TreePos, FRotator::ZeroRotator, SpawnParams
            );

            if (Tree)
            {
                GeneratedActors.Add(Tree);
            }
        }
    }
}

void AAURACRONPCGEnvironment::GenerateTectonicBridges()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 BridgeCount = FMath::RandRange(
        FAURACRONMapDimensions::TECTONIC_BRIDGES_COUNT_MIN,
        FAURACRONMapDimensions::TECTONIC_BRIDGES_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < BridgeCount; ++i)
    {
        // Gerar pontos de inÃ­cio e fim para a ponte
        float StartAngle = FMath::RandRange(0.0f, 2.0f * PI);
        float EndAngle = StartAngle + FMath::RandRange(PI * 0.3f, PI * 0.8f);

        FVector StartPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.5f * FMath::Cos(StartAngle),
            EnvironmentRadius * 0.5f * FMath::Sin(StartAngle),
            FMath::RandRange(100.0f, 300.0f) // Altura variÃ¡vel
        );

        FVector EndPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.5f * FMath::Cos(EndAngle),
            EnvironmentRadius * 0.5f * FMath::Sin(EndAngle),
            FMath::RandRange(100.0f, 300.0f) // Altura variÃ¡vel
        );

        float BridgeWidth = FMath::RandRange(200.0f, 400.0f); // 2-4 metros
        int32 NumSupports = FMath::RandRange(3, 6);
        float ArchHeight = FMath::RandRange(200.0f, 500.0f); // 2-5 metros

        // Gerar pontos da ponte
        TArray<FVector> BridgePoints = UAURACRONPCGMathLibrary::GenerateTectonicBridgePoints(
            StartPoint,
            EndPoint,
            BridgeWidth,
            NumSupports,
            ArchHeight
        );

        // Criar segmentos da ponte
        for (const FVector& BridgePoint : BridgePoints)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;

            AStaticMeshActor* BridgeSegment = GetWorld()->SpawnActor<AStaticMeshActor>(
                BridgePoint, FRotator::ZeroRotator, SpawnParams
            );

            if (BridgeSegment)
            {
                GeneratedActors.Add(BridgeSegment);
            }
        }
    }
}

// ========================================
// IMPLEMENTAÃ‡Ã•ES PARA ZEPHYR FIRMAMENT
// ========================================

void AAURACRONPCGEnvironment::GenerateOrbitalArchipelagos()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 ArchipelagoCount = FMath::RandRange(
        FAURACRONMapDimensions::ORBITAL_ARCHIPELAGOS_COUNT_MIN,
        FAURACRONMapDimensions::ORBITAL_ARCHIPELAGOS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < ArchipelagoCount; ++i)
    {
        // Cada arquipÃ©lago tem um centro orbital
        float OrbitRadius = FMath::RandRange(EnvironmentRadius * 0.3f, EnvironmentRadius * 0.7f);
        float OrbitAngle = (2.0f * PI * i / ArchipelagoCount) + FMath::RandRange(-0.5f, 0.5f);

        FVector ArchipelagoCenter = EnvironmentCenter + FVector(
            OrbitRadius * FMath::Cos(OrbitAngle),
            OrbitRadius * FMath::Sin(OrbitAngle),
            FMath::RandRange(200.0f, 800.0f) // Altura variÃ¡vel
        );

        // Gerar ilhas individuais no arquipÃ©lago
        int32 IslandCount = FMath::RandRange(3, 8);
        float ArchipelagoRadius = FMath::RandRange(300.0f, 600.0f);

        TArray<FVector> IslandPositions = UAURACRONPCGMathLibrary::CreateOrbitalPath(
            ArchipelagoCenter,
            ArchipelagoRadius,
            0.0f, // Altura relativa
            IslandCount,
            FMath::RandRange(0.0f, 2.0f * PI) // Fase aleatÃ³ria
        );

        for (const FVector& IslandPos : IslandPositions)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;

            AStaticMeshActor* Island = GetWorld()->SpawnActor<AStaticMeshActor>(
                IslandPos, FRotator::ZeroRotator, SpawnParams
            );

            if (Island)
            {
                // Configurar a malha da ilha
                UStaticMeshComponent* MeshComponent = Island->GetStaticMeshComponent();
                if (MeshComponent)
                {
                    // Carregar uma malha de ilha flutuante
                    static ConstructorHelpers::FObjectFinder<UStaticMesh> IslandMeshFinder(TEXT("/Game/AURACRON/Meshes/Environment/ZephyrFirmament/SM_FloatingIsland"));
                    UStaticMesh* IslandMesh = IslandMeshFinder.Object;
                    
                    if (IslandMesh)
                    {
                        MeshComponent->SetStaticMesh(IslandMesh);
                        
                        // Escala aleatória para variedade
                        float IslandScale = FMath::RandRange(0.8f, 1.5f);
                        MeshComponent->SetWorldScale3D(FVector(IslandScale, IslandScale, IslandScale));
                        
                        // Rotação aleatória para variedade
                        MeshComponent->SetWorldRotation(FRotator(0.0f, FMath::RandRange(0.0f, 360.0f), 0.0f));
                        
                        // Configurar física
                        MeshComponent->SetMobility(EComponentMobility::Movable);
                        MeshComponent->SetSimulatePhysics(false);
                        MeshComponent->SetEnableGravity(false);
                        
                        // Adicionar efeito de flutuação
                        FFloatingIslandData IslandData;
                        IslandData.IslandActor = Island;
                        IslandData.OriginalHeight = IslandPos.Z;
                        IslandData.FloatHeight = FMath::RandRange(5.0f, 15.0f);
                        IslandData.FloatSpeed = FMath::RandRange(0.5f, 1.5f);
                        IslandData.TimeOffset = FMath::RandRange(0.0f, 2.0f * PI);
                        
                        // Armazenar para animação em Tick
                        FloatingIslandData.Add(IslandData);
                    }
                }
                
                GeneratedActors.Add(Island);
            }
        }
    }
}

void AAURACRONPCGEnvironment::GenerateAuroraBridges()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 BridgeCount = FMath::RandRange(
        FAURACRONMapDimensions::AURORA_BRIDGES_COUNT_MIN,
        FAURACRONMapDimensions::AURORA_BRIDGES_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < BridgeCount; ++i)
    {
        // Pontes Aurora conectam pontos distantes
        float StartAngle = FMath::RandRange(0.0f, 2.0f * PI);
        float EndAngle = StartAngle + FMath::RandRange(PI * 0.5f, PI * 1.5f);

        FVector StartPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.8f * FMath::Cos(StartAngle),
            EnvironmentRadius * 0.8f * FMath::Sin(StartAngle),
            FMath::RandRange(500.0f, 1000.0f)
        );

        FVector EndPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.8f * FMath::Cos(EndAngle),
            EnvironmentRadius * 0.8f * FMath::Sin(EndAngle),
            FMath::RandRange(500.0f, 1000.0f)
        );

        // Criar curva aurora
        FAURACRONSplineCurve AuroraCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
            StartPoint,
            EndPoint,
            15, // 15 pontos de controle
            300.0f, // Amplitude de 3 metros
            1.5f // FrequÃªncia
        );

        // Distribuir pontos de luz ao longo da curva
        TArray<FVector> LightPoints = UAURACRONPCGMathLibrary::DistributePointsAlongCurve(
            AuroraCurve,
            200.0f, // EspaÃ§amento de 2 metros
            true // Alinhar Ã  tangente
        );

        // Criar sistema de partículas para a ponte aurora
        UNiagaraComponent* AuroraBridge = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            AuroraBridgeParticleSystem,
            StartPoint,
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
        
        if (AuroraBridge)
        {
            // Configurar parâmetros do sistema de partículas
            AuroraBridge->SetVectorParameter("StartPoint", StartPoint);
            AuroraBridge->SetVectorParameter("EndPoint", EndPoint);
            AuroraBridge->SetFloatParameter("BridgeWidth", FMath::RandRange(50.0f, 150.0f));
            
            // Cor baseada no ciclo dia/noite (simulado com tempo do mundo)
            float TimeOfDay = FMath::Frac(GetWorld()->GetTimeSeconds() / 300.0f); // Ciclo de 5 minutos
            FLinearColor BridgeColor;
            
            if (TimeOfDay < 0.5f) // Dia
            {
                BridgeColor = FLinearColor(0.2f, 0.8f, 1.0f, 0.8f); // Azul claro
            }
            else // Noite
            {
                BridgeColor = FLinearColor(0.8f, 0.2f, 1.0f, 0.8f); // Roxo
            }
            
            AuroraBridge->SetColorParameter("BridgeColor", BridgeColor);
            
            // Visibilidade baseada no ciclo dia/noite
            bool bIsVisible = (TimeOfDay > 0.4f && TimeOfDay < 0.6f) || // Transição dia/noite
                             (TimeOfDay > 0.9f || TimeOfDay < 0.1f);   // Transição noite/dia
            
            AuroraBridge->SetVisibility(bIsVisible, true);
            
            // Adicionar à lista de componentes gerados para limpeza posterior
            // (AuroraBridge é um UNiagaraComponent*, não AActor*)
            if (AuroraBridge->GetOwner())
            {
                GeneratedActors.Add(AuroraBridge->GetOwner());
            }
            
            // Criar colisão para a ponte (invisível, apenas para permitir movimento)
            for (const FVector& LightPoint : LightPoints)
            {
                FActorSpawnParameters SpawnParams;
                SpawnParams.Owner = this;
                
                AStaticMeshActor* BridgeCollision = GetWorld()->SpawnActor<AStaticMeshActor>(
                    LightPoint, FRotator::ZeroRotator, SpawnParams
                );
                
                if (BridgeCollision)
                {
                    // Configurar colisão invisível
                    UStaticMeshComponent* MeshComponent = BridgeCollision->GetStaticMeshComponent();
                    if (MeshComponent)
                    {
                        // Usar uma malha simples para colisão
                        static ConstructorHelpers::FObjectFinder<UStaticMesh> CollisionMeshFinder(TEXT("/Engine/BasicShapes/Cube"));
                        UStaticMesh* CollisionMesh = CollisionMeshFinder.Object;
                        
                        if (CollisionMesh)
                        {
                            MeshComponent->SetStaticMesh(CollisionMesh);
                            MeshComponent->SetWorldScale3D(FVector(0.5f, 0.5f, 0.1f));
                            MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                            MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);
                            MeshComponent->SetVisibility(false);
                        }
                    }
                    
                    GeneratedActors.Add(BridgeCollision);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::GenerateVoidRifts()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 RiftCount = FMath::RandRange(
        FAURACRONMapDimensions::VOID_RIFTS_COUNT_MIN,
        FAURACRONMapDimensions::VOID_RIFTS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < RiftCount; ++i)
    {
        // Posicionar fendas do vazio em locais estratégicos
        float RiftAngle = FMath::RandRange(0.0f, 2.0f * PI);
        float RiftRadius = FMath::RandRange(EnvironmentRadius * 0.2f, EnvironmentRadius * 0.8f);
        
        FVector RiftCenter = EnvironmentCenter + FVector(
            RiftRadius * FMath::Cos(RiftAngle),
            RiftRadius * FMath::Sin(RiftAngle),
            FMath::RandRange(300.0f, 700.0f) // Altura variável
        );
        
        // Determinar tamanho e orientação da fenda
        float RiftSize = FMath::RandRange(200.0f, 500.0f);
        FRotator RiftRotation = FRotator(
            FMath::RandRange(-30.0f, 30.0f),  // Pitch
            FMath::RandRange(0.0f, 360.0f),  // Yaw
            FMath::RandRange(-15.0f, 15.0f)  // Roll
        );
        
        // Criar a fenda principal
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = this;
        
        AStaticMeshActor* VoidRift = GetWorld()->SpawnActor<AStaticMeshActor>(
            RiftCenter, RiftRotation, SpawnParams
        );
        
        if (VoidRift)
        {
            // Configurar a malha e materiais da fenda
            UStaticMeshComponent* MeshComp = VoidRift->GetStaticMeshComponent();
            if (MeshComp)
            {
                // Configurar escala baseada no tamanho desejado
                MeshComp->SetWorldScale3D(FVector(RiftSize / 100.0f));
                
                // Adicionar efeitos de partículas e distorção visual
                UNiagaraComponent* RiftEffect = NewObject<UNiagaraComponent>(VoidRift);
                if (RiftEffect)
                {
                    RiftEffect->RegisterComponent();
                    RiftEffect->AttachToComponent(MeshComp, FAttachmentTransformRules::KeepRelativeTransform);
                    // Configurar sistema de partículas para efeito de fenda
                }
            }
            
            GeneratedActors.Add(VoidRift);
            
            // Gerar pequenas fendas satélites ao redor da principal
            int32 SatelliteCount = FMath::RandRange(2, 5);
            for (int32 j = 0; j < SatelliteCount; ++j)
            {
                float SatelliteAngle = FMath::RandRange(0.0f, 2.0f * PI);
                float SatelliteDistance = FMath::RandRange(RiftSize * 0.5f, RiftSize * 1.5f);
                
                FVector SatellitePos = RiftCenter + FVector(
                    SatelliteDistance * FMath::Cos(SatelliteAngle),
                    SatelliteDistance * FMath::Sin(SatelliteAngle),
                    FMath::RandRange(-100.0f, 100.0f) // Variação de altura
                );
                
                FRotator SatelliteRot = RiftRotation + FRotator(
                    FMath::RandRange(-45.0f, 45.0f),
                    FMath::RandRange(-45.0f, 45.0f),
                    FMath::RandRange(-45.0f, 45.0f)
                );
                
                AStaticMeshActor* SatelliteRift = GetWorld()->SpawnActor<AStaticMeshActor>(
                    SatellitePos, SatelliteRot, SpawnParams
                );
                
                if (SatelliteRift)
                {
                    UStaticMeshComponent* SatMeshComp = SatelliteRift->GetStaticMeshComponent();
                    if (SatMeshComp)
                    {
                        // Escala menor para fendas satélites
                        float SatelliteScale = RiftSize * FMath::RandRange(0.2f, 0.4f) / 100.0f;
                        SatMeshComp->SetWorldScale3D(FVector(SatelliteScale));
                    }
                    
                    GeneratedActors.Add(SatelliteRift);
                }
            }
        }
    }
}

// Fim das implementações das funções de geração específicas

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES DE ATUALIZAÇÃO DINÂMICA
// ========================================

void AAURACRONPCGEnvironment::UpdateDynamicElements(float DeltaTime)
{
    // Atualizar elementos dinâmicos com base no tipo de ambiente
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Calcular posição do sol para comportamento dinâmico
    float TimeOfDay = FMath::Fmod(CurrentTime / 3600.0f, 24.0f); // Ciclo de 24 horas
    float SunAngle = (TimeOfDay / 24.0f) * 2.0f * PI;
    float SunElevation = FMath::Sin(SunAngle) * 90.0f; // -90 a +90 graus
    float SunAzimuth = (TimeOfDay / 24.0f) * 360.0f; // 0 a 360 graus
    float SolarIntensity = FMath::Max(0.0f, SunElevation / 90.0f); // 0.0 a 1.0
    float LunarIntensity = FMath::Max(0.0f, -SunElevation / 90.0f); // Inverso do solar
    
    // Aplicar efeitos solares globais via PCG
    SetPCGParameterModern(TEXT("SolarIntensity"), FVector(SolarIntensity, 0.0f, 0.0f), TEXT("SolarSystem"));
    SetPCGParameterModern(TEXT("LunarIntensity"), FVector(LunarIntensity, 0.0f, 0.0f), TEXT("SolarSystem"));
    SetPCGParameterModern(TEXT("SunElevation"), FVector(SunElevation, 0.0f, 0.0f), TEXT("SolarSystem"));
    SetPCGParameterModern(TEXT("SunAzimuth"), FVector(SunAzimuth, 0.0f, 0.0f), TEXT("SolarSystem"));
    SetPCGParameterModern(TEXT("TimeOfDay"), FVector(TimeOfDay, 0.0f, 0.0f), TEXT("SolarSystem"));
    
    // Calcular direção solar para efeitos direcionais
    FVector SunDirection = FVector(
        FMath::Cos(FMath::DegreesToRadians(SunAzimuth)) * FMath::Cos(FMath::DegreesToRadians(SunElevation)),
        FMath::Sin(FMath::DegreesToRadians(SunAzimuth)) * FMath::Cos(FMath::DegreesToRadians(SunElevation)),
        FMath::Sin(FMath::DegreesToRadians(SunElevation))
    );
    SetPCGParameterModern(TEXT("SunDirection"), SunDirection, TEXT("SolarSystem"));
    
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Radiant Plains são mais afetados pela luz solar
        {
            float RadiantMultiplier = 1.0f + (SolarIntensity * 0.5f); // Até 50% mais intenso durante o dia
            SetPCGParameterModern(TEXT("RadiantMultiplier"), FVector(RadiantMultiplier, 0.0f, 0.0f), TEXT("RadiantPlains"));
            
            // Cristalline Plateaus brilham mais durante o dia
            if (bHasCrystallinePlateaus)
            {
                float CrystalGlow = SolarIntensity * 2.0f; // Brilho baseado na intensidade solar
                SetPCGParameterModern(TEXT("CrystalGlow"), FVector(CrystalGlow, 0.0f, 0.0f), TEXT("CrystallinePlateaus"));
            }
            
            if (bHasBreathingForests) UpdateBreathingForests(CurrentTime);
            if (bHasTectonicBridges) UpdateTectonicBridges(CurrentTime);
        }
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Zephyr Firmament responde aos ventos solares
        {
            float WindIntensity = 0.5f + (SolarIntensity * 0.8f); // Ventos mais fortes durante o dia
            float AuroralActivity = SolarIntensity * LunarIntensity * 4.0f; // Máximo durante crepúsculo
            
            SetPCGParameterModern(TEXT("WindIntensity"), FVector(WindIntensity, 0.0f, 0.0f), TEXT("ZephyrFirmament"));
            SetPCGParameterModern(TEXT("AuroralActivity"), FVector(AuroralActivity, 0.0f, 0.0f), TEXT("ZephyrFirmament"));
            
            if (bHasOrbitalArchipelagos) UpdateOrbitalArchipelagos(CurrentTime);
            if (bHasAuroraBridges) UpdateAuroraBridges(CurrentTime);
            if (bHasCloudFortresses) UpdateCloudFortresses(CurrentTime);
            if (bHasStellarGardens) UpdateStellarGardens(CurrentTime);
        }
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Purgatory Realm é mais ativo durante a noite
        {
            float LocalShadowIntensity = LunarIntensity * 1.5f; // Sombras mais intensas à noite
            float LocalSpectralActivity = LunarIntensity * 2.0f; // Atividade espectral noturna
            float TemporalDistortion = 1.0f + (LunarIntensity * 0.3f); // Distorção temporal aumenta à noite
            
            SetPCGParameterModern(TEXT("ShadowIntensity"), FVector(LocalShadowIntensity, 0.0f, 0.0f), TEXT("PurgatoryRealm"));
            SetPCGParameterModern(TEXT("SpectralActivity"), FVector(LocalSpectralActivity, 0.0f, 0.0f), TEXT("PurgatoryRealm"));
            SetPCGParameterModern(TEXT("TemporalDistortion"), FVector(TemporalDistortion, 0.0f, 0.0f), TEXT("PurgatoryRealm"));
            
            if (bHasTemporalDistortionZones) UpdateTemporalDistortionZones(CurrentTime);
            if (bHasRiversOfSouls) UpdateRiversOfSouls(CurrentTime);
            if (bHasFragmentedStructures) UpdateFragmentedStructures(CurrentTime);
            if (bHasShadowStructures) {
                UpdateShadowNexuses(CurrentTime);
                UpdateTowersOfLamentation(CurrentTime);
                UpdateSpectralGuardian(CurrentTime);
                UpdatePurgatoryAnchor(CurrentTime);
            }
        }
        break;
    }
    
    // Regenerar PCG se houver mudanças significativas na intensidade solar
    static float LastSolarIntensity = -1.0f;
    if (FMath::Abs(SolarIntensity - LastSolarIntensity) > 0.1f)
    {
        if (PCGComponent)
        {
            PCGComponent->Generate();
        }
        LastSolarIntensity = SolarIntensity;
    }
}

void AAURACRONPCGEnvironment::UpdateOrbitalArchipelagos(float Time)
{
    // Atualizar ilhas flutuantes dos arquipélagos orbitais
    for (FFloatingIslandData& IslandData : FloatingIslandData)
    {
        if (IslandData.IslandActor && IslandData.IslandActor->GetStaticMeshComponent())
        {
            // Calcular movimento de flutuação baseado em seno
            float FloatOffset = FMath::Sin((Time + IslandData.TimeOffset) * IslandData.FloatSpeed) * IslandData.FloatHeight;
            
            // Aplicar movimento vertical
            FVector CurrentLocation = IslandData.IslandActor->GetActorLocation();
            FVector NewLocation = FVector(CurrentLocation.X, CurrentLocation.Y, IslandData.OriginalHeight + FloatOffset);
            IslandData.IslandActor->SetActorLocation(NewLocation);
            
            // Aplicar leve rotação para efeito de balanço
            float RotationAmount = FMath::Sin((Time + IslandData.TimeOffset) * IslandData.FloatSpeed * 0.5f) * 0.5f;
            FRotator CurrentRotation = IslandData.IslandActor->GetActorRotation();
            FRotator NewRotation = FRotator(CurrentRotation.Pitch + RotationAmount, CurrentRotation.Yaw, CurrentRotation.Roll + RotationAmount * 0.7f);
            IslandData.IslandActor->SetActorRotation(NewRotation);
        }
    }
}

void AAURACRONPCGEnvironment::UpdateAuroraBridges(float Time)
{
    // Atualizar pontes aurora com efeitos de ondulação e cores dinâmicas
    for (FAuroraBridgeData& BridgeData : AuroraBridgeData)
    {
        if (BridgeData.BridgeActor && IsValid(BridgeData.BridgeActor))
        {
            // Atualizar sistema de partículas se existir
            TArray<UActorComponent*> Components = BridgeData.BridgeActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UNiagaraComponent* AuroraEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Calcular intensidade baseada no ciclo dia/noite
                    float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                    float AuroraIntensity = 0.5f + (ActivityScale * 0.5f);
                    
                    // Intensidade maior durante a noite
                    if (TimeOfDay >= 0.5f) // Noite
                    {
                        AuroraIntensity *= 1.5f;
                    }
                    
                    // Aplicar ondulação baseada em seno
                    float WaveIntensity = FMath::Sin(Time * 0.2f) * 0.3f + 0.7f;
                    AuroraEffect->SetFloatParameter("AuroraIntensity", AuroraIntensity * WaveIntensity);
                    
                    // Variar cores ao longo do tempo
                    float HueShift = FMath::Frac(Time * 0.05f); // Mudança lenta de cor
                    FLinearColor BaseColor = FLinearColor(0.2f, 0.4f, 1.0f); // Azul base
                    FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                    AuroraEffect->SetColorParameter("AuroraColor", ShiftedColor);
                    
                    // Ajustar velocidade de fluxo
                    float FlowSpeed = 0.8f + FMath::Sin(Time * 0.1f) * 0.2f;
                    AuroraEffect->SetFloatParameter("FlowSpeed", FlowSpeed);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateStellarGardens(float Time)
{
    // Atualizar jardins estelares com rotação e brilho dinâmicos
    for (FStellarGardenData& GardenData : StellarGardenData)
    {
        if (GardenData.GardenActor && IsValid(GardenData.GardenActor))
        {
            // Aplicar rotação lenta
            FRotator CurrentRotation = GardenData.GardenActor->GetActorRotation();
            float RotationSpeed = GardenData.RotationSpeed * ActivityScale;
            FRotator NewRotation = FRotator(CurrentRotation.Pitch, CurrentRotation.Yaw + RotationSpeed * Time * 0.1f, CurrentRotation.Roll);
            GardenData.GardenActor->SetActorRotation(NewRotation);
            
            // Atualizar materiais dinâmicos para efeito de brilho pulsante
            TArray<UActorComponent*> Components = GardenData.GardenActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        // Calcular brilho pulsante
                        float GlowIntensity = 0.8f + FMath::Sin(Time * GardenData.PulseSpeed) * 0.2f;
                        GlowIntensity *= ActivityScale;
                        
                        // Aplicar parâmetros
                        DynamicMaterial->SetScalarParameterValue(TEXT("GlowIntensity"), GlowIntensity);
                        
                        // Variar cor ao longo do tempo
                        float HueShift = FMath::Frac(Time * 0.02f); // Mudança muito lenta de cor
                        FLinearColor BaseColor = FLinearColor(0.8f, 0.9f, 1.0f); // Azul claro base
                        FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                        DynamicMaterial->SetVectorParameterValue(TEXT("GlowColor"), ShiftedColor);
                    }
                }
                
                // Atualizar sistemas de partículas
                if (UNiagaraComponent* StarEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Ajustar taxa de spawn baseada na atividade
                    float SpawnRate = 10.0f + (ActivityScale * 20.0f);
                    StarEffect->SetFloatParameter("SpawnRate", SpawnRate);
                    
                    // Ajustar brilho
                    float StarBrightness = 0.7f + FMath::Sin(Time * 0.3f) * 0.3f;
                    StarBrightness *= ActivityScale;
                    StarEffect->SetFloatParameter("StarBrightness", StarBrightness);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateBreathingForests(float Time)
{
    // Atualizar florestas respirantes com movimento de "respiração"
    for (FBreathingForestData& ForestData : BreathingForestData)
    {
        if (ForestData.TreeActor && IsValid(ForestData.TreeActor))
        {
            // Calcular ciclo de respiração
            float BreathCycle = FMath::Sin(Time * ForestData.BreathSpeed) * 0.5f + 0.5f; // Normalizado entre 0 e 1
            
            // Aplicar escala para simular respiração
            float BaseScale = ForestData.OriginalScale;
            float BreathScale = BaseScale * (1.0f + (BreathCycle * ForestData.BreathAmount * ActivityScale));
            FVector NewScale = FVector(BreathScale, BreathScale, BreathScale * (1.0f + BreathCycle * 0.1f));
            ForestData.TreeActor->SetActorScale3D(NewScale);
            
            // Atualizar materiais para efeito de "vida"
            TArray<UActorComponent*> Components = ForestData.TreeActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        // Pulsar cor baseado no ciclo de respiração
                        float ColorIntensity = 0.8f + (BreathCycle * 0.2f);
                        DynamicMaterial->SetScalarParameterValue(TEXT("EmissiveIntensity"), ColorIntensity * ActivityScale);
                        
                        // Ajustar cor baseado no ciclo dia/noite
                        float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                        FLinearColor EmissiveColor;
                        
                        if (TimeOfDay < 0.5f) // Dia
                        {
                            EmissiveColor = FLinearColor(0.2f, 0.8f, 0.3f, 1.0f); // Verde mais claro
                        }
                        else // Noite
                        {
                            EmissiveColor = FLinearColor(0.1f, 0.5f, 0.2f, 1.0f); // Verde mais escuro
                        }
                        
                        DynamicMaterial->SetVectorParameterValue(TEXT("EmissiveColor"), EmissiveColor);
                    }
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateRiversOfSouls(float Time)
{
    // Atualizar rios de almas com fluxo dinâmico
    for (FRiverOfSoulsData& RiverData : RiversOfSoulsData)
    {
        if (RiverData.RiverActor && IsValid(RiverData.RiverActor))
        {
            // Atualizar sistema de partículas para fluxo de almas
            TArray<UActorComponent*> Components = RiverData.RiverActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UNiagaraComponent* SoulEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Ajustar velocidade de fluxo baseado na atividade
                    float FlowSpeed = RiverData.BaseFlowSpeed * (0.8f + ActivityScale * 0.4f);
                    SoulEffect->SetFloatParameter("FlowSpeed", FlowSpeed);
                    
                    // Ajustar densidade de almas
                    float SoulDensity = RiverData.BaseSoulDensity * ActivityScale;
                    SoulEffect->SetFloatParameter("SoulDensity", SoulDensity);
                    
                    // Ajustar brilho baseado no ciclo dia/noite
                    float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                    float SoulBrightness;
                    
                    if (TimeOfDay < 0.5f) // Dia
                    {
                        SoulBrightness = 0.7f; // Menos brilhante durante o dia
                    }
                    else // Noite
                    {
                        SoulBrightness = 1.2f; // Mais brilhante durante a noite
                    }
                    
                    SoulEffect->SetFloatParameter("SoulBrightness", SoulBrightness * ActivityScale);
                    
                    // Variar cor ao longo do tempo
                    float HueShift = FMath::Frac(Time * 0.01f); // Mudança muito lenta de cor
                    FLinearColor BaseColor = FLinearColor(0.5f, 0.8f, 1.0f); // Azul espectral base
                    FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                    SoulEffect->SetColorParameter("SoulColor", ShiftedColor);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateFragmentedStructures(float Time)
{
    // Atualizar estruturas fragmentadas com movimento de flutuação e rotação
    for (FFragmentedStructureData& StructureData : FragmentedStructureData)
    {
        if (StructureData.StructureActor && IsValid(StructureData.StructureActor))
        {
            // Calcular movimento de flutuação baseado em seno
            float FloatOffset = FMath::Sin((Time + StructureData.TimeOffset) * StructureData.FloatSpeed) * StructureData.FloatHeight;
            
            // Aplicar movimento vertical
            FVector CurrentLocation = StructureData.StructureActor->GetActorLocation();
            FVector NewLocation = CurrentLocation;
            NewLocation.Z = StructureData.OriginalHeight + FloatOffset;
            StructureData.StructureActor->SetActorLocation(NewLocation);
            
            // Aplicar rotação lenta
            FRotator CurrentRotation = StructureData.StructureActor->GetActorRotation();
            float RotationAmount = FMath::Sin((Time + StructureData.TimeOffset) * StructureData.FloatSpeed * 0.3f) * 0.8f;
            FRotator NewRotation = FRotator(
                CurrentRotation.Pitch + RotationAmount * StructureData.RotationFactor.X,
                CurrentRotation.Yaw + (Time * StructureData.RotationSpeed * 0.1f),
                CurrentRotation.Roll + RotationAmount * StructureData.RotationFactor.Z
            );
            StructureData.StructureActor->SetActorRotation(NewRotation);
            
            // Atualizar materiais para efeito de fragmentação
            TArray<UActorComponent*> Components = StructureData.StructureActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        // Ajustar dissolução baseada na atividade
                        float DissolveAmount = 0.2f + (1.0f - ActivityScale) * 0.3f;
                        DynamicMaterial->SetScalarParameterValue(TEXT("DissolveAmount"), DissolveAmount);
                        
                        // Ajustar brilho das bordas
                        float EdgeGlow = 0.5f + FMath::Sin(Time * 0.4f) * 0.3f;
                        EdgeGlow *= ActivityScale;
                        DynamicMaterial->SetScalarParameterValue(TEXT("EdgeGlowIntensity"), EdgeGlow);
                        
                        // Ajustar cor baseado no ciclo dia/noite
                        float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                        FLinearColor GlowColor;
                        
                        if (TimeOfDay < 0.5f) // Dia
                        {
                            GlowColor = FLinearColor(0.8f, 0.3f, 0.1f, 1.0f); // Laranja-avermelhado
                        }
                        else // Noite
                        {
                            GlowColor = FLinearColor(0.5f, 0.1f, 0.3f, 1.0f); // Roxo-avermelhado
                        }
                        
                        DynamicMaterial->SetVectorParameterValue(TEXT("EdgeGlowColor"), GlowColor);
                    }
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateTemporalDistortionZones(float Time)
{
    // Atualizar zonas de distorção temporal com efeitos de ondulação e distorção
    for (FTemporalDistortionData& DistortionData : TemporalDistortionData)
    {
        if (DistortionData.DistortionActor && IsValid(DistortionData.DistortionActor))
        {
            // Calcular intensidade de distorção baseada em seno
            float DistortionIntensity = 0.5f + FMath::Sin(Time * DistortionData.PulseSpeed) * 0.3f;
            DistortionIntensity *= ActivityScale;
            
            // Atualizar materiais para efeito de distorção
            TArray<UActorComponent*> Components = DistortionData.DistortionActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        // Aplicar intensidade de distorção
                        DynamicMaterial->SetScalarParameterValue(TEXT("DistortionIntensity"), DistortionIntensity);
                        
                        // Ajustar velocidade de distorção
                        float DistortionSpeed = 0.5f + FMath::Sin(Time * 0.2f) * 0.2f;
                        DynamicMaterial->SetScalarParameterValue(TEXT("DistortionSpeed"), DistortionSpeed);
                        
                        // Ajustar cor baseado no tempo
                        float HueShift = FMath::Frac(Time * 0.03f); // Mudança lenta de cor
                        FLinearColor BaseColor = FLinearColor(0.3f, 0.1f, 0.5f); // Roxo base
                        FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                        DynamicMaterial->SetVectorParameterValue(TEXT("DistortionColor"), ShiftedColor);
                    }
                }
                
                // Atualizar sistemas de partículas
                if (UNiagaraComponent* DistortionEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Ajustar intensidade do efeito
                    DistortionEffect->SetFloatParameter("EffectIntensity", DistortionIntensity);
                    
                    // Ajustar velocidade de rotação
                    float RotationSpeed = 0.3f + FMath::Sin(Time * 0.15f) * 0.2f;
                    DistortionEffect->SetFloatParameter("RotationSpeed", RotationSpeed);
                    
                    // Ajustar cor do efeito
                    float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                    FLinearColor EffectColor;
                    
                    if (TimeOfDay < 0.5f) // Dia
                    {
                        EffectColor = FLinearColor(0.4f, 0.2f, 0.6f, 0.7f); // Roxo mais claro
                    }
                    else // Noite
                    {
                        EffectColor = FLinearColor(0.2f, 0.1f, 0.4f, 0.9f); // Roxo mais escuro e opaco
                    }
                    
                    DistortionEffect->SetColorParameter("EffectColor", EffectColor);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateCloudFortresses(float Time)
{
    // Atualizar fortalezas de nuvem com movimento de deriva circular e flutuação vertical
    for (FCloudFortressData& FortressData : CloudFortressData)
    {
        if (FortressData.FortressActor && IsValid(FortressData.FortressActor))
        {
            // Calcular movimento de deriva circular
            float DriftAngle = (Time + FortressData.TimeOffset) * FortressData.DriftSpeed;
            
            // Calcular nova posição com base no movimento circular
            FVector DriftOffset = FVector(
                FortressData.DriftRadius * FMath::Cos(DriftAngle),
                FortressData.DriftRadius * FMath::Sin(DriftAngle),
                0.0f
            );
            
            // Calcular movimento de flutuação vertical
            float VerticalOffset = FortressData.VerticalAmplitude * FMath::Sin((Time + FortressData.TimeOffset) * FortressData.VerticalSpeed);
            
            // Aplicar movimento combinado
            FVector NewLocation = FortressData.OriginalPosition + DriftOffset + FVector(0.0f, 0.0f, VerticalOffset);
            FortressData.FortressActor->SetActorLocation(NewLocation);
            
            // Aplicar leve rotação para efeito de balanço
            float RotationAmount = FMath::Sin((Time + FortressData.TimeOffset) * FortressData.VerticalSpeed * 0.5f) * 2.0f;
            FRotator CurrentRotation = FortressData.FortressActor->GetActorRotation();
            FRotator NewRotation = FRotator(CurrentRotation.Pitch + RotationAmount, CurrentRotation.Yaw, CurrentRotation.Roll + RotationAmount * 0.7f);
            FortressData.FortressActor->SetActorRotation(NewRotation);
            
            // Atualizar efeitos de partículas se existirem
            TArray<UActorComponent*> Components = FortressData.FortressActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UNiagaraComponent* CloudEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Ajustar intensidade do efeito de nuvem com base na atividade
                    float CloudIntensity = 0.5f + (ActivityScale * 0.5f);
                    CloudEffect->SetFloatParameter("CloudIntensity", CloudIntensity);
                    
                    // Ajustar cor com base no ciclo dia/noite (simulado com tempo do mundo)
                    float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                    FLinearColor CloudColor;
                    
                    if (TimeOfDay < 0.5f) // Dia
                    {
                        CloudColor = FLinearColor(1.0f, 1.0f, 1.0f, 0.8f); // Branco
                    }
                    else // Noite
                    {
                        CloudColor = FLinearColor(0.7f, 0.8f, 1.0f, 0.8f); // Azul claro
                    }
                    
                    CloudEffect->SetColorParameter("CloudColor", CloudColor);
                }
            }
        }
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGEnvironment::RegisterWithEnvironmentManager()
{
    // Buscar o Environment Manager no mundo usando as APIs modernas do UE 5.6
    if (UWorld* World = GetWorld())
    {
        // Usar o sistema de subsistemas moderno do UE 5.6
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            // Buscar o Environment Manager através do subsistema
            TArray<AActor*> FoundManagers;
            UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGEnvironmentManager::StaticClass(), FoundManagers);

            if (FoundManagers.Num() > 0)
            {
                if (AAURACRONPCGEnvironmentManager* Manager = Cast<AAURACRONPCGEnvironmentManager>(FoundManagers[0]))
                {
                    // Registrar esta instância usando API moderna
                    Manager->RegisterEnvironmentInstance(this);

                    // Log usando sistema moderno do UE 5.6
                    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::RegisterWithEnvironmentManager - Environment %s registered successfully"), *GetName());
                }
            }
            else
            {
                // Criar um Environment Manager se não existir (padrão moderno UE 5.6)
                FActorSpawnParameters SpawnParams;
                SpawnParams.Name = FName(TEXT("AURACRONPCGEnvironmentManager"));
                SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

                if (AAURACRONPCGEnvironmentManager* NewManager = World->SpawnActor<AAURACRONPCGEnvironmentManager>(SpawnParams))
                {
                    NewManager->RegisterEnvironmentInstance(this);
                    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::RegisterWithEnvironmentManager - Created new manager and registered environment %s"), *GetName());
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::ActivateEnvironment()
{
    // Ativar usando APIs modernas do UE 5.6
    if (PCGComponent && IsValid(PCGComponent))
    {
        // Usar a API moderna de ativação do PCG Component
        PCGComponent->SetComponentTickEnabled(true);

        // Ativar geração procedural usando API moderna
        if (PCGComponent->GetGraph())
        {
            // Executar geração usando sistema moderno do UE 5.6
            PCGComponent->GenerateLocal(true);

            // Marcar como ativo
            bIsActive = true;

            // Aplicar configurações de qualidade baseadas na performance
            ApplyQualitySettings();

            // Log usando sistema moderno
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::ActivateEnvironment - Environment %s activated with modern UE 5.6 APIs"), *GetName());
        }
    }

    // Ativar componentes relacionados usando APIs modernas
    TArray<UActorComponent*> Components = GetComponents().Array();
    for (UActorComponent* Component : Components)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            MeshComp->SetComponentTickEnabled(true);
            MeshComp->SetVisibility(true);
        }
    }
}

void AAURACRONPCGEnvironment::DeactivateEnvironment()
{
    // Desativar usando APIs modernas do UE 5.6
    if (PCGComponent && IsValid(PCGComponent))
    {
        // Usar a API moderna de desativação do PCG Component
        PCGComponent->SetComponentTickEnabled(false);

        // Limpar geração procedural usando API moderna
        PCGComponent->CleanupLocal(true);

        // Marcar como inativo
        bIsActive = false;

        // Log usando sistema moderno
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::DeactivateEnvironment - Environment %s deactivated with modern UE 5.6 APIs"), *GetName());
    }

    // Desativar componentes relacionados usando APIs modernas
    TArray<UActorComponent*> Components = GetComponents().Array();
    for (UActorComponent* Component : Components)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            MeshComp->SetComponentTickEnabled(false);
            MeshComp->SetVisibility(false);
        }
    }
}

void AAURACRONPCGEnvironment::ApplyTransitionEffect(float TransitionAlpha, bool bFadeIn)
{
    // Aplicar efeitos de transição usando APIs modernas do UE 5.6
    if (!IsValid(this))
    {
        return;
    }

    // Calcular alpha efetivo baseado na direção da transição
    float EffectiveAlpha = bFadeIn ? TransitionAlpha : (1.0f - TransitionAlpha);

    // Aplicar transição aos componentes usando APIs modernas
    TArray<UActorComponent*> Components = GetComponents().Array();
    for (UActorComponent* Component : Components)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            // Usar API moderna de materiais dinâmicos do UE 5.6
            if (UMaterialInterface* Material = MeshComp->GetMaterial(0))
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    // Aplicar parâmetros de transição usando API moderna
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("TransitionAlpha")), EffectiveAlpha);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("Opacity")), EffectiveAlpha);

                    // Aplicar efeitos baseados no tipo de ambiente
                    switch (EnvironmentType)
                    {
                        case EAURACRONEnvironmentType::RadiantPlains:
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("RadiantGlow")), FLinearColor(1.0f, 0.9f, 0.7f, EffectiveAlpha));
                            break;
                        case EAURACRONEnvironmentType::ZephyrFirmament:
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("ZephyrFlow")), FLinearColor(0.7f, 0.9f, 1.0f, EffectiveAlpha));
                            if (bHasVoidRifts) {
                                DynamicMaterial->SetVectorParameterValue(FName(TEXT("VoidDistortion")), FLinearColor(0.3f, 0.1f, 0.5f, EffectiveAlpha * 0.8f));
                            }
                            break;
                        case EAURACRONEnvironmentType::PurgatoryRealm:
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("PurgatoryAura")), FLinearColor(0.9f, 0.3f, 0.3f, EffectiveAlpha));
                            break;
                        default:
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("DefaultGlow")), FLinearColor(1.0f, 1.0f, 1.0f, EffectiveAlpha));
                            break;
                    }
                }
            }
        }
    }

    // Aplicar transição ao PCG Component usando APIs modernas
    if (PCGComponent && IsValid(PCGComponent))
    {
        // Usar sistema moderno de parâmetros dinâmicos do UE 5.6
        if (PCGComponent->GetGraph())
        {
            // Aplicar parâmetros de transição ao grafo PCG
            SetPCGParameterModern(TEXT("TransitionAlpha"), FVector(EffectiveAlpha, 0.0f, 0.0f), TEXT("TransitionEffect"));
            SetPCGParameterModern(TEXT("FadeDirection"), FVector(bFadeIn ? 1.0f : 0.0f, 0.0f, 0.0f), TEXT("TransitionEffect"));
        }
    }

    // Log usando sistema moderno
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::ApplyTransitionEffect - Applied transition alpha %.2f (FadeIn: %s) to environment %s"),
           EffectiveAlpha, bFadeIn ? TEXT("true") : TEXT("false"), *GetName());
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES DE GERAÇÃO ESPECÍFICAS - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGEnvironment::GenerateCloudFortresses()
{
    // Verificar autoridade de rede e componente PCG
    if (!HasAuthority() || !PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateCloudFortresses - Invalid state or no authority"));
        return;
    }

    // Limpar dados anteriores
    CloudFortressData.Empty();

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float CloudHeight = 2000.0f; // 20 metros acima do solo

    // Gerar posições para fortalezas usando algoritmo moderno
    TArray<FVector> FortressPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        MapCenter + FVector(0.0f, 0.0f, CloudHeight),
        5000.0f, // Raio de 50 metros
        1000.0f, // Distância mínima de 10 metros entre fortalezas
        30, // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );

    // Determinar características baseadas na fase do mapa
    FLinearColor FortressGlowColor = FLinearColor(1.0f, 0.8f, 0.6f); // Cor padrão
    float PhaseIntensity = 1.0f;

    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();

            switch (CurrentPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    PhaseIntensity = 0.7f;
                    FortressGlowColor = FLinearColor(1.0f, 0.8f, 0.6f); // Dourado suave
                    break;
                case EAURACRONMapPhase::Convergence:
                    PhaseIntensity = 1.0f;
                    FortressGlowColor = FLinearColor(1.0f, 1.0f, 1.0f); // Branco brilhante
                    break;
                case EAURACRONMapPhase::Intensification:
                    PhaseIntensity = 0.8f;
                    FortressGlowColor = FLinearColor(1.0f, 0.6f, 0.4f); // Laranja intenso
                    break;
                case EAURACRONMapPhase::Resolution:
                    PhaseIntensity = 0.5f;
                    FortressGlowColor = FLinearColor(0.4f, 0.6f, 1.0f); // Azul calmo
                    break;
            }
        }
    }

    // Criar fortalezas nas nuvens em cada posição gerada
    for (int32 i = 0; i < FortressPositions.Num(); i++)
    {
        // Calcular posição com variação de altura
        FVector FortressPosition = FortressPositions[i];
        FortressPosition.Z += FMath::RandRange(-200.0f, 200.0f); // Variação de altura

        // Criar ator de malha estática para a fortaleza
        AStaticMeshActor* FortressActor = GetWorld()->SpawnActor<AStaticMeshActor>(AStaticMeshActor::StaticClass(), 
                                                                                 FTransform(FortressPosition));
        if (FortressActor)
        {
            // Configurar nome único
            // Usar UObject::Rename para definir o nome do ator no UE 5.6
            FString NewName = FString::Printf(TEXT("CloudFortress_%d"), i);
            FortressActor->Rename(*NewName);

            // Configurar malha
            UStaticMeshComponent* MeshComponent = FortressActor->GetStaticMeshComponent();
            if (MeshComponent)
            {
                // Carregar malha de fortaleza
                static ConstructorHelpers::FObjectFinder<UStaticMesh> FortressMeshFinder(TEXT("/Game/AURACRON/Meshes/Environment/ZephyrFirmament/SM_CloudFortress"));
                if (FortressMeshFinder.Succeeded())
                {
                    MeshComponent->SetStaticMesh(FortressMeshFinder.Object);
                }
                else
                {
                    // Fallback para cubo básico se a malha não for encontrada
                    static ConstructorHelpers::FObjectFinder<UStaticMesh> FallbackMeshFinder(TEXT("/Engine/BasicShapes/Cube"));
                    if (FallbackMeshFinder.Succeeded())
                    {
                        MeshComponent->SetStaticMesh(FallbackMeshFinder.Object);
                    }
                }
                
                // Configurar escala aleatória
                float BaseScale = FMath::RandRange(0.8f, 1.2f);
                FortressActor->SetActorScale3D(FVector(BaseScale, BaseScale, BaseScale * FMath::RandRange(0.9f, 1.1f)));
                
                // Configurar rotação aleatória
                FRotator RandomRotation(0.0f, FMath::RandRange(0.0f, 360.0f), 0.0f);
                FortressActor->SetActorRotation(RandomRotation);
                
                // Configurar material dinâmico
                UMaterialInterface* BaseMaterial = MeshComponent->GetMaterial(0);
                if (BaseMaterial)
                {
                    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, FortressActor);
                    if (DynamicMaterial)
                    {
                        // Configurar parâmetros do material
                        DynamicMaterial->SetVectorParameterValue(TEXT("GlowColor"), FortressGlowColor);
                        DynamicMaterial->SetScalarParameterValue(TEXT("GlowIntensity"), PhaseIntensity);
                        DynamicMaterial->SetScalarParameterValue(TEXT("CloudOpacity"), FMath::RandRange(0.7f, 0.9f));
                        
                        // Aplicar material dinâmico
                        MeshComponent->SetMaterial(0, DynamicMaterial);
                    }
                }
                
                // Configurar física
                MeshComponent->SetSimulatePhysics(false);
                MeshComponent->SetEnableGravity(false);
                MeshComponent->SetCollisionProfileName(TEXT("BlockAllDynamic"));
                
                // Adicionar efeito de nuvem (partículas)
                UNiagaraComponent* CloudEffect = NewObject<UNiagaraComponent>(FortressActor);
                if (CloudEffect)
                {
                    CloudEffect->RegisterComponent();
                    CloudEffect->AttachToComponent(MeshComponent, FAttachmentTransformRules::KeepRelativeTransform);
                    
                    // Configurar sistema de partículas
                    static ConstructorHelpers::FObjectFinder<UNiagaraSystem> CloudSystemFinder(TEXT("/Game/AURACRON/FX/Environment/NS_CloudAmbient"));
                    if (CloudSystemFinder.Succeeded())
                    {
                        CloudEffect->SetAsset(CloudSystemFinder.Object);
                        CloudEffect->SetColorParameter(TEXT("CloudColor"), FortressGlowColor);
                        CloudEffect->SetFloatParameter(TEXT("CloudDensity"), FMath::RandRange(0.8f, 1.2f));
                    }
                }
                
                // Criar dados para movimento de deriva
                FCloudFortressData FortressData;
                FortressData.FortressActor = FortressActor;
                FortressData.OriginalPosition = FortressPosition;
                FortressData.DriftRadius = FMath::RandRange(200.0f, 400.0f);
                FortressData.DriftSpeed = FMath::RandRange(0.1f, 0.3f);
                FortressData.TimeOffset = FMath::RandRange(0.0f, 2.0f * PI);
                FortressData.VerticalAmplitude = FMath::RandRange(30.0f, 70.0f);
                FortressData.VerticalSpeed = FMath::RandRange(0.3f, 0.7f);
                
                // Adicionar à lista de fortalezas
                CloudFortressData.Add(FortressData);



                // Configurar física
                MeshComponent->SetSimulatePhysics(false);
                MeshComponent->SetEnableGravity(false);
                MeshComponent->SetCollisionProfileName(TEXT("BlockAllDynamic"));



                // Adicionar movimento de deriva
                FortressActor->SetMobility(EComponentMobility::Movable);

                // Armazenar dados para movimento de deriva usando FFloatingIslandData
                FFloatingIslandData FloatingData;
                FloatingData.IslandActor = FortressActor;
                FloatingData.OriginalHeight = FortressPosition.Z;
                FloatingData.FloatHeight = FMath::RandRange(20.0f, 50.0f); // Amplitude de flutuação
                FloatingData.FloatSpeed = FMath::RandRange(0.1f, 0.3f); // Velocidade de flutuação
                FloatingData.TimeOffset = FMath::RandRange(0.0f, 2.0f * PI); // Offset de tempo para movimento não sincronizado

                // Adicionar à lista de ilhas flutuantes para animação
                FloatingIslandData.Add(FloatingData);

                // Adicionar à lista de atores gerados
                GeneratedActors.Add(FortressActor);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateCloudFortresses - Generated %d cloud fortresses"), CloudFortressData.Num());
}

void AAURACRONPCGEnvironment::GenerateStellarGardens()
{
    // Gerar jardins estelares usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateStellarGardens - PCGComponent is invalid"));
        return;
    }
    
    // Limpar dados anteriores
    StellarGardenData.Empty();
    
    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float GardenHeight = 1800.0f; // Altura dos jardins estelares
    
    // Gerar posições para jardins estelares usando distribuição de Poisson
    int32 NumGardens = FMath::RandRange(3, 7); // Número de jardins estelares
    
    TArray<FVector> GardenPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        MapCenter + FVector(0.0f, 0.0f, GardenHeight),
        4000.0f, // Raio de distribuição
        1200.0f, // Distância mínima entre jardins
        30,      // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );
    
    // Limitar ao número desejado de jardins
    if (GardenPositions.Num() > NumGardens)
    {
        GardenPositions.SetNum(NumGardens);
    }
    
    // Determinar características baseadas na fase do mapa
    FLinearColor GardenGlowColor = FLinearColor(0.2f, 0.8f, 1.0f); // Cor padrão azul celeste
    float PhaseIntensity = 1.0f;
    
    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
            
            // Ajustar cor e intensidade com base na fase do mapa
            switch (CurrentPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    GardenGlowColor = FLinearColor(0.2f, 0.8f, 1.0f); // Azul celeste
                    PhaseIntensity = 0.7f;
                    break;
                    
                case EAURACRONMapPhase::Expansion:
                    GardenGlowColor = FLinearColor(0.5f, 0.9f, 1.0f); // Azul mais brilhante
                    PhaseIntensity = 1.0f;
                    break;
                    
                case EAURACRONMapPhase::Convergence:
                    GardenGlowColor = FLinearColor(0.7f, 1.0f, 1.0f); // Azul intenso
                    PhaseIntensity = 1.3f;
                    break;
                    
                case EAURACRONMapPhase::Intensification:
                    GardenGlowColor = FLinearColor(0.9f, 1.0f, 1.0f); // Quase branco
                    PhaseIntensity = 1.5f;
                    break;
                    
                default:
                    break;
            }
        }
    }
    
    // Criar jardins estelares
    for (const FVector& Position : GardenPositions)
    {
        // Criar ator de malha estática para o jardim
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        AStaticMeshActor* GardenActor = GetWorld()->SpawnActor<AStaticMeshActor>(
            Position,
            FRotator::ZeroRotator,
            SpawnParams
        );
        
        if (GardenActor)
        {
            // Configurar a malha do jardim estelar
            UStaticMeshComponent* MeshComp = GardenActor->GetStaticMeshComponent();
            if (MeshComp)
            {
                // Carregar malha para o jardim estelar
                static ConstructorHelpers::FObjectFinder<UStaticMesh> GardenMeshFinder(TEXT("/Game/AURACRON/Environment/ZephyrFirmament/Meshes/SM_StellarGarden"));
                if (GardenMeshFinder.Succeeded())
                {
                    MeshComp->SetStaticMesh(GardenMeshFinder.Object);
                }
                
                // Configurar material com efeito de brilho
                static ConstructorHelpers::FObjectFinder<UMaterialInterface> GardenMaterialFinder(TEXT("/Game/AURACRON/Environment/ZephyrFirmament/Materials/M_StellarGarden"));
                if (GardenMaterialFinder.Succeeded())
                {
                    MeshComp->SetMaterial(0, GardenMaterialFinder.Object);
                    
                    // Criar instância de material dinâmica
                    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(GardenMaterialFinder.Object, GardenActor);
                    if (DynamicMaterial)
                    {
                        // Configurar parâmetros do material
                        DynamicMaterial->SetVectorParameterValue(TEXT("GlowColor"), GardenGlowColor);
                        DynamicMaterial->SetScalarParameterValue(TEXT("GlowIntensity"), PhaseIntensity);
                        
                        MeshComp->SetMaterial(0, DynamicMaterial);
                    }
                }
                
                // Configurar física e colisão
                MeshComp->SetMobility(EComponentMobility::Movable);
                MeshComp->SetCollisionProfileName(TEXT("BlockAll"));
                MeshComp->SetGenerateOverlapEvents(true);
                
                // Configurar escala aleatória para variedade
                float Scale = FMath::RandRange(0.8f, 1.2f);
                GardenActor->SetActorScale3D(FVector(Scale, Scale, Scale));
                
                // Adicionar dados para animação
                FStellarGardenData GardenData;
                GardenData.GardenActor = GardenActor;
                GardenData.Position = Position;
                GardenData.RotationSpeed = FMath::RandRange(0.05f, 0.15f);
                GardenData.PulsationAmplitude = FMath::RandRange(0.1f, 0.3f);
                GardenData.PulsationSpeed = FMath::RandRange(0.3f, 0.7f);
                GardenData.TimeOffset = FMath::RandRange(0.0f, 2.0f * PI);
                
                // Adicionar à lista de jardins estelares para animação
                StellarGardenData.Add(GardenData);
            }
            
            // Adicionar à lista de atores gerados
            GeneratedActors.Add(GardenActor);
        }
    }
    
    // Aplicar parâmetros ao PCG para efeitos adicionais
    SetPCGParameterModern(TEXT("GardenPositions"), MapCenter + FVector(0.0f, 0.0f, GardenHeight), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenCount"), FVector(GardenPositions.Num(), 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenHeight"), FVector(GardenHeight, 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("PhaseIntensity"), FVector(PhaseIntensity, 0.0f, 0.0f), TEXT("StellarGardens"));
    // Converter FLinearColor para FVector no UE 5.6
    FVector GardenGlowVector(GardenGlowColor.R, GardenGlowColor.G, GardenGlowColor.B);
    SetPCGParameterModern(TEXT("GardenGlow"), GardenGlowVector, TEXT("StellarGardens"));
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateStellarGardens - Generated %d stellar gardens using modern UE 5.6 APIs"), GardenPositions.Num());

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("GardenCenter"), MapCenter, TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenCount"), FVector(GardenPositions.Num(), 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenRadius"), FVector(3000.0f, 0.0f, 0.0f), TEXT("StellarGardens"));

    // Aplicar efeitos baseados no tempo usando APIs modernas
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float StellarPulse = (FMath::Sin(CurrentTime * 0.5f) + 1.0f) * 0.5f; // Normalizado entre 0 e 1
    float StellarGlow = 0.8f + 0.2f * FMath::Cos(CurrentTime * 0.3f);

    SetPCGParameterModern(TEXT("StellarPulse"), FVector(StellarPulse, 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("StellarGlow"), FVector(StellarGlow, 0.0f, 0.0f), TEXT("StellarGardens"));

    // Aplicar características baseadas na fase lunar
    float LunarIntensity = UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(CurrentTime / 86400.0f); // Converter para dias
    SetPCGParameterModern(TEXT("LunarIntensity"), FVector(LunarIntensity, 0.0f, 0.0f), TEXT("StellarGardens"));

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateStellarGardens - Generated %d stellar gardens using modern UE 5.6 APIs"), GardenPositions.Num());
}

void AAURACRONPCGEnvironment::GenerateSpectralPlains()
{
    // Gerar planícies espectrais usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateSpectralPlains - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;

    // Configurar padrão de ruído para planícies espectrais
    FAURACRONNoisePattern SpectralNoise;
    SpectralNoise.Frequency = 0.01f; // Baixa frequência para planícies suaves
    SpectralNoise.Amplitude = 200.0f; // Amplitude de 2 metros
    SpectralNoise.Octaves = 4;
    SpectralNoise.Persistence = 0.6f;
    SpectralNoise.Lacunarity = 2.0f;
    SpectralNoise.Seed = FMath::Rand();

    // Gerar mapa de altura usando algoritmo moderno
    TArray<float> HeightMap = UAURACRONPCGMathLibrary::GenerateHeightMap(
        100, // Largura
        100, // Altura
        SpectralNoise,
        -100.0f, // Altura mínima
        300.0f // Altura máxima
    );

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("PlainsCenter"), MapCenter, TEXT("SpectralPlains"));
    SetPCGParameterModern(TEXT("NoiseFrequency"), FVector(SpectralNoise.Frequency, 0.0f, 0.0f), TEXT("SpectralPlains"));
    SetPCGParameterModern(TEXT("NoiseAmplitude"), FVector(SpectralNoise.Amplitude, 0.0f, 0.0f), TEXT("SpectralPlains"));

    // Aplicar efeitos espectrais baseados no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float SpectralShift = FMath::Sin(CurrentTime * 0.2f) * 0.5f + 0.5f; // Normalizado entre 0 e 1
    float SpectralIntensity = 0.7f + 0.3f * FMath::Cos(CurrentTime * 0.4f);

    SetPCGParameterModern(TEXT("SpectralShift"), FVector(SpectralShift, 0.0f, 0.0f), TEXT("SpectralPlains"));
    SetPCGParameterModern(TEXT("SpectralIntensity"), FVector(SpectralIntensity, 0.0f, 0.0f), TEXT("SpectralPlains"));

    // Aplicar cores espectrais baseadas na fase do mapa
    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
            FLinearColor SpectralColor;

            switch (CurrentPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    SpectralColor = FLinearColor(0.8f, 0.6f, 1.0f, 0.7f); // Roxo suave
                    break;
                case EAURACRONMapPhase::Convergence:
                    SpectralColor = FLinearColor(0.6f, 0.8f, 1.0f, 0.8f); // Azul claro
                    break;
                case EAURACRONMapPhase::Intensification:
                    SpectralColor = FLinearColor(1.0f, 0.6f, 0.8f, 0.7f); // Rosa
                    break;
                case EAURACRONMapPhase::Resolution:
                    SpectralColor = FLinearColor(0.4f, 0.4f, 1.0f, 0.9f); // Azul escuro
                    break;
            }

            SetPCGParameterModern(TEXT("SpectralColor"), FVector(SpectralColor.R, SpectralColor.G, SpectralColor.B), TEXT("SpectralPlains"));
        }
    }

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateSpectralPlains - Generated spectral plains with %d height points using modern UE 5.6 APIs"), HeightMap.Num());
}

void AAURACRONPCGEnvironment::GenerateRiversOfSouls()
{
    // Gerar rios de almas usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateRiversOfSouls - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    FVector RiverStart = MapCenter + FVector(-4000.0f, -2000.0f, 0.0f);
    FVector RiverEnd = MapCenter + FVector(4000.0f, 2000.0f, 0.0f);

    // Criar curva serpentina para o rio usando algoritmo moderno
    FAURACRONSplineCurve RiverCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
        RiverStart,
        RiverEnd,
        15, // Número de pontos de controle
        800.0f, // Amplitude das curvas (8 metros)
        1.5f // Frequência das curvas
    );

    // Distribuir pontos ao longo da curva
    TArray<FVector> RiverPoints = UAURACRONPCGMathLibrary::DistributePointsAlongCurve(
        RiverCurve,
        200.0f, // Espaçamento de 2 metros
        true // Alinhar com tangente
    );

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("RiverStart"), RiverStart, TEXT("RiversOfSouls"));
    SetPCGParameterModern(TEXT("RiverEnd"), RiverEnd, TEXT("RiversOfSouls"));
    SetPCGParameterModern(TEXT("RiverPointCount"), FVector(RiverPoints.Num(), 0.0f, 0.0f), TEXT("RiversOfSouls"));

    // Aplicar efeitos de almas baseados no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float SoulFlow = FMath::Sin(CurrentTime * 0.8f) * 0.3f + 0.7f; // Fluxo entre 0.4 e 1.0
    float SoulDensity = 0.6f + 0.4f * FMath::Cos(CurrentTime * 0.6f);
    float SoulGlow = (FMath::Sin(CurrentTime * 1.2f) + 1.0f) * 0.5f; // Normalizado entre 0 e 1

    SetPCGParameterModern(TEXT("SoulFlow"), FVector(SoulFlow, 0.0f, 0.0f), TEXT("RiversOfSouls"));
    SetPCGParameterModern(TEXT("SoulDensity"), FVector(SoulDensity, 0.0f, 0.0f), TEXT("RiversOfSouls"));
    SetPCGParameterModern(TEXT("SoulGlow"), FVector(SoulGlow, 0.0f, 0.0f), TEXT("RiversOfSouls"));

    // Aplicar cor das almas baseada na intensidade solar
    float SolarIntensity = UAURACRONPCGMathLibrary::GetSolarIntensity(CurrentTime / 86400.0f);
    FLinearColor SoulColor = FLinearColor::LerpUsingHSV(
        FLinearColor(0.2f, 0.8f, 1.0f, 0.8f), // Azul claro durante o dia
        FLinearColor(0.8f, 0.2f, 1.0f, 0.9f), // Roxo durante a noite
        1.0f - SolarIntensity
    );

    SetPCGParameterModern(TEXT("SoulColor"), FVector(SoulColor.R, SoulColor.G, SoulColor.B), TEXT("RiversOfSouls"));

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateRiversOfSouls - Generated river of souls with %d points using modern UE 5.6 APIs"), RiverPoints.Num());
}

void AAURACRONPCGEnvironment::GenerateFragmentedStructures()
{
    // Gerar estruturas fragmentadas usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateFragmentedStructures - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;

    // Gerar posições fragmentadas usando amostragem de Poisson
    TArray<FVector> FragmentPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        MapCenter,
        6000.0f, // Raio de 60 metros
        500.0f, // Distância mínima de 5 metros entre fragmentos
        40, // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );

    // Aplicar variação de altura para fragmentos flutuantes
    for (FVector& Position : FragmentPositions)
    {
        float HeightVariation = FMath::RandRange(100.0f, 1500.0f); // Entre 1 e 15 metros de altura
        Position.Z += HeightVariation;
    }

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("FragmentCenter"), MapCenter, TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentCount"), FVector(FragmentPositions.Num(), 0.0f, 0.0f), TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentRadius"), FVector(6000.0f, 0.0f, 0.0f), TEXT("FragmentedStructures"));

    // Aplicar efeitos de fragmentação baseados no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float FragmentRotation = CurrentTime * 10.0f; // Rotação lenta
    float FragmentFloat = FMath::Sin(CurrentTime * 0.5f) * 50.0f; // Flutuação de 0.5 metros
    float FragmentGlow = 0.5f + 0.5f * FMath::Sin(CurrentTime * 0.7f);

    SetPCGParameterModern(TEXT("FragmentRotation"), FVector(FragmentRotation, 0.0f, 0.0f), TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentFloat"), FVector(FragmentFloat, 0.0f, 0.0f), TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentGlow"), FVector(FragmentGlow, 0.0f, 0.0f), TEXT("FragmentedStructures"));

    // Aplicar características baseadas na atividade do mapa
    float ActivityLevel = ActivityScale;
    float FragmentStability = 1.0f - (ActivityLevel * 0.3f); // Menos estabilidade com mais atividade
    float FragmentEnergy = ActivityLevel * 2.0f; // Mais energia com mais atividade

    SetPCGParameterModern(TEXT("FragmentStability"), FVector(FragmentStability, 0.0f, 0.0f), TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentEnergy"), FVector(FragmentEnergy, 0.0f, 0.0f), TEXT("FragmentedStructures"));

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateFragmentedStructures - Generated %d fragmented structures using modern UE 5.6 APIs"), FragmentPositions.Num());
}



void AAURACRONPCGEnvironment::CreateVoidRiftPortal(const FVector& Position, float Radius, const FLinearColor& Color)
{
    // Verificar se o mundo é válido
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::CreateVoidRiftPortal - World is invalid"));
        return;
    }
    
    // Criar portal usando a classe AAURACRONPCGPortal
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    
    AAURACRONPCGPortal* Portal = World->SpawnActor<AAURACRONPCGPortal>(
        AAURACRONPCGPortal::StaticClass(), 
        Position, 
        FRotator::ZeroRotator, 
        SpawnParams
    );
    
    if (!Portal)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironment::CreateVoidRiftPortal - Failed to spawn portal"));
        return;
    }
    
    // Obter um destino aleatório para o teletransporte
    // Idealmente, deveria ser outra fenda do vazio em uma plataforma distante
    // Para esta implementação, vamos usar uma posição aleatória elevada
    FVector RandomOffset = FVector(
        FMath::RandRange(-5000.0f, 5000.0f),
        FMath::RandRange(-5000.0f, 5000.0f),
        FMath::RandRange(1000.0f, 3000.0f) // Altura elevada para simular plataformas celestiais
    );
    
    FVector DestinationLocation = FAURACRONMapDimensions::MAP_CENTER + RandomOffset;
    FRotator DestinationRotation = FRotator::ZeroRotator;
    
    // Configurar o portal
    FAURACRONPortalSettings PortalSettings;
    PortalSettings.CurrentEnvironment = EAURACRONEnvironmentType::ZephyrFirmament;
    PortalSettings.DestinationLocation = DestinationLocation;
    PortalSettings.DestinationRotation = DestinationRotation;
    PortalSettings.PortalScale = Radius * 0.5f; // Escala proporcional ao raio da fenda
    PortalSettings.PortalColor = Color;
    PortalSettings.EffectIntensity = 1.5f; // Intensidade aumentada para fendas do vazio
    PortalSettings.ActivationRadius = Radius * 100.0f; // Raio de ativação em centímetros
    PortalSettings.bIsActive = true;
    PortalSettings.bIsVisible = true;
    
    // Inicializar o portal
    Portal->InitializePortal(PortalSettings);
    
    // Adicionar à lista de atores gerados para limpeza posterior
    GeneratedActors.Add(Portal);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::CreateVoidRiftPortal - Created void rift portal at %s with destination %s"),
           *Position.ToString(), *DestinationLocation.ToString());
}

void AAURACRONPCGEnvironment::GenerateTemporalDistortionZones()
{
    // Gerar zonas de distorção temporal usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateTemporalDistortionZones - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;

    // Gerar posições para zonas de distorção usando padrão hexagonal
    TArray<FVector> DistortionZones = UAURACRONPCGMathLibrary::GenerateHexagonalGrid(
        MapCenter,
        4000.0f, // Raio de 40 metros
        1200.0f, // Espaçamento de 12 metros
        true, // Adicionar offset aleatório
        0.4f // Quantidade de offset aleatório
    );

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("DistortionCenter"), MapCenter, TEXT("TemporalDistortionZones"));
    SetPCGParameterModern(TEXT("DistortionCount"), FVector(DistortionZones.Num(), 0.0f, 0.0f), TEXT("TemporalDistortionZones"));
    SetPCGParameterModern(TEXT("DistortionRadius"), FVector(4000.0f, 0.0f, 0.0f), TEXT("TemporalDistortionZones"));

    // Aplicar efeitos temporais baseados no tempo real
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float TemporalWave = FMath::Sin(CurrentTime * 0.3f) * 0.5f + 0.5f; // Normalizado entre 0 e 1
    float TemporalIntensity = 0.8f + 0.2f * FMath::Cos(CurrentTime * 0.4f);
    float TemporalFrequency = 1.0f + 0.5f * FMath::Sin(CurrentTime * 0.2f);

    SetPCGParameterModern(TEXT("TemporalWave"), FVector(TemporalWave, 0.0f, 0.0f), TEXT("TemporalDistortionZones"));
    SetPCGParameterModern(TEXT("TemporalIntensity"), FVector(TemporalIntensity, 0.0f, 0.0f), TEXT("TemporalDistortionZones"));
    SetPCGParameterModern(TEXT("TemporalFrequency"), FVector(TemporalFrequency, 0.0f, 0.0f), TEXT("TemporalDistortionZones"));

    // Aplicar efeitos baseados na posição temporal usando APIs modernas
    float TimeOfDay = FMath::Fmod(CurrentTime / 86400.0f, 1.0f); // Normalizar para ciclo de 24h
    FVector TimeBasedPosition = UAURACRONPCGMathLibrary::GetTimeBasedPosition(
        MapCenter,
        TimeOfDay,
        300.0f, // Amplitude de 3 metros
        2.0f // Frequência
    );

    SetPCGParameterModern(TEXT("TimeBasedPosition"), TimeBasedPosition, TEXT("TemporalDistortionZones"));

    // Aplicar cor temporal baseada na fase lunar
    float LunarIntensity = UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(TimeOfDay);
    FLinearColor TemporalColor = FLinearColor::LerpUsingHSV(
        FLinearColor(0.6f, 0.2f, 1.0f, 0.7f), // Roxo durante lua nova
        FLinearColor(1.0f, 0.8f, 0.2f, 0.8f), // Dourado durante lua cheia
        LunarIntensity
    );

    SetPCGParameterModern(TEXT("TemporalColor"), FVector(TemporalColor.R, TemporalColor.G, TemporalColor.B), TEXT("TemporalDistortionZones"));

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateTemporalDistortionZones - Generated %d temporal distortion zones using modern UE 5.6 APIs"), DistortionZones.Num());
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES AUXILIARES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGEnvironment::ApplyQualitySettings()
{
    // Aplicar configurações de qualidade baseadas na performance usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        return;
    }

    // Obter configurações de qualidade do sistema moderno
    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            // Obter nível de qualidade atual
            int32 QualityLevel = PCGSubsystem->GetCurrentQualityLevel();

            // Aplicar configurações baseadas na qualidade
            switch (QualityLevel)
            {
                case 0: // Baixa qualidade
                {
                    SetPCGParameterModern(TEXT("QualityLevel"), FVector(0.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("DetailLevel"), FVector(0.5f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("ParticleCount"), FVector(50.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    break;
                }

                case 1: // Qualidade média
                {
                    SetPCGParameterModern(TEXT("QualityLevel"), FVector(1.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("DetailLevel"), FVector(0.75f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("ParticleCount"), FVector(100.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    break;
                }

                case 2: // Alta qualidade
                {
                    SetPCGParameterModern(TEXT("QualityLevel"), FVector(2.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("DetailLevel"), FVector(1.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("ParticleCount"), FVector(200.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    break;
                }

                default: // Qualidade épica
                {
                    SetPCGParameterModern(TEXT("QualityLevel"), FVector(3.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("DetailLevel"), FVector(1.5f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("ParticleCount"), FVector(400.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    break;
                }
            }

            // Aplicar configurações de LOD baseadas na distância
            float LODDistance = QualityLevel * 1000.0f + 2000.0f; // Entre 2km e 5km
            SetPCGParameterModern(TEXT("LODDistance"), FVector(LODDistance, 0.0f, 0.0f), TEXT("QualitySettings"));

            // Aplicar configurações de culling
            bool bEnableCulling = QualityLevel < 3; // Desabilitar culling apenas na qualidade épica
            SetPCGParameterModern(TEXT("EnableCulling"), FVector(bEnableCulling ? 1.0f : 0.0f, 0.0f, 0.0f), TEXT("QualitySettings"));

            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::ApplyQualitySettings - Applied quality level %d settings using modern UE 5.6 APIs"), QualityLevel);
        }
    }
}

void AAURACRONPCGEnvironment::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	
	// Replicar propriedades básicas do ambiente
	DOREPLIFETIME(AAURACRONPCGEnvironment, EnvironmentType);
	DOREPLIFETIME(AAURACRONPCGEnvironment, ActivityScale);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bIsActive);
	
	// Replicar propriedades específicas do Radiant Plains
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasCrystallinePlateaus);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasOrbitalArchipelagos);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasSpectralPlains);
	
	// Replicar propriedades específicas do Zephyr Firmament
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasAerialIslands);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasFloatingCrystals);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasWindCurrents);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasVoidRifts);
	
	// Replicar propriedades específicas do Purgatory Realm
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasAbyssalChasms);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasEtherealFog);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasTemporalDistortions);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasShadowStructures);
}

FAURACRONEnvironmentConfig AAURACRONPCGEnvironment::GetEnvironmentConfiguration() const
{
    // Obter configuração do ambiente usando dados modernos do UE 5.6
    FAURACRONEnvironmentConfig Config;

    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            Config.PrimaryColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado radiante
            Config.LightIntensity = 1.4f;
            Config.MaterialRoughness = 0.25f;
            Config.ActivityScale = 1.3f;
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            Config.PrimaryColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul aéreo
            Config.LightIntensity = 1.2f;
            Config.MaterialRoughness = 0.15f;
            Config.ActivityScale = 1.1f;
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            Config.PrimaryColor = FLinearColor(0.9f, 0.3f, 0.3f, 1.0f); // Vermelho sombrio
            Config.LightIntensity = 0.7f;
            Config.MaterialRoughness = 0.8f;
            Config.ActivityScale = 1.8f;
            break;

        default:
            Config.PrimaryColor = FLinearColor::White;
            Config.LightIntensity = 1.0f;
            Config.MaterialRoughness = 0.5f;
            Config.ActivityScale = 1.0f;
            break;
    }

    // Aplicar modificações baseadas na escala de atividade atual
    Config.ActivityScale *= ActivityScale;
    Config.LightIntensity *= (1.0f + (ActivityScale - 1.0f) * 0.5f);

    return Config;
}

void AAURACRONPCGEnvironment::SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Category)
{
    // Definir parâmetro PCG usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de parâmetros do UE 5.6
    if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
    {
        // Usar API moderna para definir parâmetros via GraphInstance
        if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
        {
            // Criar nome completo do parâmetro com categoria
            FString FullParameterName = Category.IsEmpty() ? ParameterName : FString::Printf(TEXT("%s.%s"), *Category, *ParameterName);
            FName ParamName = FName(*FullParameterName);

            // Usar API moderna SetGraphParameter do UE 5.6
            EPropertyBagResult Result = GraphInstance->SetGraphParameter<FVector>(ParamName, Value);

            if (Result == EPropertyBagResult::Success)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - Successfully set FVector parameter %s = (%.2f, %.2f, %.2f)"),
                       *FullParameterName, Value.X, Value.Y, Value.Z);
            }
            else if (Result == EPropertyBagResult::PropertyNotFound)
            {
                // Tentar definir como float usando apenas X
                EPropertyBagResult FloatResult = GraphInstance->SetGraphParameter<float>(ParamName, Value.X);
                if (FloatResult == EPropertyBagResult::Success)
                {
                    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - Successfully set float parameter %s = %.2f"),
                           *FullParameterName, Value.X);
                }
                else
                {
                    // Tentar definir como double
                    EPropertyBagResult DoubleResult = GraphInstance->SetGraphParameter<double>(ParamName, static_cast<double>(Value.X));
                    if (DoubleResult == EPropertyBagResult::Success)
                    {
                        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - Successfully set double parameter %s = %.2f"),
                               *FullParameterName, Value.X);
                    }
                    else
                    {
                        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - Failed to set parameter %s as FVector, float, or double. Result: %d"),
                               *FullParameterName, (int32)DoubleResult);
                    }
                }
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - Failed to set FVector parameter %s. Result: %d"),
                       *FullParameterName, (int32)Result);
            }

            // Regenerar PCG com novos parâmetros
            PCGComponent->GenerateLocal(true);

            // Log para debug
            UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - Setting parameter %s with value (%f, %f, %f)"),
                   *FullParameterName, Value.X, Value.Y, Value.Z);

            // Forçar regeneração do PCG se necessário
            if (bIsActive && PCGComponent->GetGraph())
            {
                // Usar API moderna para regeneração incremental
                PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
            }

            UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - Set parameter %s = (%.2f, %.2f, %.2f) in category %s"),
                   *ParameterName, Value.X, Value.Y, Value.Z, *Category);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - GraphInstance not found"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - PCG Graph not found"));
    }
}





UStaticMesh* AAURACRONPCGEnvironment::GetAnchorMeshForEnvironment(EAURACRONEnvironmentType EnvType) const
{
    // Retornar mesh específico baseado no tipo de ambiente
    switch (EnvType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            static ConstructorHelpers::FObjectFinder<UStaticMesh> RadiantMeshFinder(TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
            return RadiantMeshFinder.Succeeded() ? RadiantMeshFinder.Object : nullptr;
        }
        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            static ConstructorHelpers::FObjectFinder<UStaticMesh> ZephyrMeshFinder(TEXT("/Engine/BasicShapes/Cone.Cone"));
            return ZephyrMeshFinder.Succeeded() ? ZephyrMeshFinder.Object : nullptr;
        }
        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            static ConstructorHelpers::FObjectFinder<UStaticMesh> PurgatoryMeshFinder(TEXT("/Engine/BasicShapes/Cube.Cube"));
            return PurgatoryMeshFinder.Succeeded() ? PurgatoryMeshFinder.Object : nullptr;
        }
        default:
            return nullptr;
    }
}

UMaterialInterface* AAURACRONPCGEnvironment::GetAnchorMaterialForEnvironment(EAURACRONEnvironmentType EnvType) const
{
    // Retornar material específico baseado no tipo de ambiente
    switch (EnvType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            static ConstructorHelpers::FObjectFinder<UMaterialInterface> RadiantMaterialFinder(TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
            return RadiantMaterialFinder.Succeeded() ? RadiantMaterialFinder.Object : nullptr;
        }
        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            static ConstructorHelpers::FObjectFinder<UMaterialInterface> ZephyrMaterialFinder(TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
            return ZephyrMaterialFinder.Succeeded() ? ZephyrMaterialFinder.Object : nullptr;
        }
        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            static ConstructorHelpers::FObjectFinder<UMaterialInterface> PurgatoryMaterialFinder(TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
            return PurgatoryMaterialFinder.Succeeded() ? PurgatoryMaterialFinder.Object : nullptr;
        }
        default:
            return nullptr;
    }
}



void AAURACRONPCGEnvironment::SetDensityScale(float DensityScale)
{
    // Implementação da configuração de escala de densidade
    if (PCGComponent && PCGComponent->GetGraph())
    {
        // Configurar parâmetro de densidade usando API moderna do UE 5.6
        SetPCGParameterModern(TEXT("DensityScale"), FVector(DensityScale, 0.0f, 0.0f), TEXT("EnvironmentDensity"));

        // Regenerar PCG com nova densidade
        PCGComponent->GenerateLocal(true);

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::SetDensityScale - Densidade configurada para %f"), DensityScale);
    }
}

float AAURACRONPCGEnvironment::GetCurrentPhaseIntensity() const
{
    // Implementação robusta para obter intensidade da fase atual
    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
            switch (CurrentPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    return 0.3f;
                case EAURACRONMapPhase::Expansion:
                    return 0.5f;
                case EAURACRONMapPhase::Convergence:
                    return 0.7f;
                case EAURACRONMapPhase::Intensification:
                    return 1.2f;
                case EAURACRONMapPhase::Resolution:
                    return 1.5f;
                default:
                    return 1.0f;
            }
        }
    }
    return 1.0f;
}

FLinearColor AAURACRONPCGEnvironment::GetPhaseBasedColor() const
{
    // Implementação robusta para obter cor baseada na fase
    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
            switch (CurrentPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    return FLinearColor(0.3f, 0.8f, 1.0f, 1.0f); // Azul claro
                case EAURACRONMapPhase::Expansion:
                    return FLinearColor(0.5f, 1.0f, 0.5f, 1.0f); // Verde
                case EAURACRONMapPhase::Convergence:
                    return FLinearColor(1.0f, 0.8f, 0.2f, 1.0f); // Amarelo
                case EAURACRONMapPhase::Intensification:
                    return FLinearColor(1.0f, 0.2f, 0.2f, 1.0f); // Vermelho
                case EAURACRONMapPhase::Resolution:
                    return FLinearColor(0.8f, 0.2f, 1.0f, 1.0f); // Roxo
                default:
                    return FLinearColor::White;
            }
        }
    }
    return FLinearColor::White;
}

float AAURACRONPCGEnvironment::GetEnvironmentHeightAt(const FVector& Location) const
{
    // Implementação robusta para obter altura do ambiente usando line trace
    FVector Start = Location + FVector(0.0f, 0.0f, 10000.0f);
    FVector End = Location - FVector(0.0f, 0.0f, 10000.0f);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(const_cast<AAURACRONPCGEnvironment*>(this));

    if (GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECC_Visibility, QueryParams))
    {
        return HitResult.ImpactPoint.Z;
    }

    // Se não encontrar o chão, retornar altura padrão
    return Location.Z;
}



// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES DE GERAÇÃO FALTANTES - UE 5.6
// ========================================











TArray<FVector> AAURACRONPCGEnvironment::GetTacticalPortalLocations() const
{
    TArray<FVector> PortalLocations;

    // Gerar localizações táticas baseadas no tipo de ambiente
    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            // Portais em pontos estratégicos dos Radiant Plains
            int32 PortalCount = 4; // 4 portais principais
            for (int32 i = 0; i < PortalCount; ++i)
            {
                float Angle = (2.0f * PI * i / PortalCount);
                FVector PortalLocation = EnvironmentCenter + FVector(
                    EnvironmentRadius * 0.7f * FMath::Cos(Angle),
                    EnvironmentRadius * 0.7f * FMath::Sin(Angle),
                    100.0f // Altura padrão
                );
                PortalLocations.Add(PortalLocation);
            }
            break;
        }

        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            // Portais em ilhas flutuantes
            int32 PortalCount = 6; // 6 portais em diferentes alturas
            for (int32 i = 0; i < PortalCount; ++i)
            {
                float Angle = (2.0f * PI * i / PortalCount);
                float Height = 300.0f + (i * 150.0f); // Alturas escalonadas
                FVector PortalLocation = EnvironmentCenter + FVector(
                    EnvironmentRadius * 0.6f * FMath::Cos(Angle),
                    EnvironmentRadius * 0.6f * FMath::Sin(Angle),
                    Height
                );
                PortalLocations.Add(PortalLocation);
            }
            break;
        }

        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            // Portais em locais sombrios e estratégicos
            int32 PortalCount = 5; // 5 portais em pentágono
            for (int32 i = 0; i < PortalCount; ++i)
            {
                float Angle = (2.0f * PI * i / PortalCount) + (PI / 5.0f); // Offset para pentágono
                FVector PortalLocation = EnvironmentCenter + FVector(
                    EnvironmentRadius * 0.8f * FMath::Cos(Angle),
                    EnvironmentRadius * 0.8f * FMath::Sin(Angle),
                    50.0f // Altura baixa para ambiente sombrio
                );
                PortalLocations.Add(PortalLocation);
            }
            break;
        }

        default:
        {
            // Configuração padrão
            PortalLocations.Add(EnvironmentCenter + FVector(EnvironmentRadius * 0.5f, 0.0f, 100.0f));
            PortalLocations.Add(EnvironmentCenter + FVector(-EnvironmentRadius * 0.5f, 0.0f, 100.0f));
            break;
        }
    }

    return PortalLocations;
}

void AAURACRONPCGEnvironment::OnMapContraction(float ContractionFactor)
{
    UE_LOG(LogTemp, Log, TEXT("OnMapContraction - Aplicando contração %.2f ao ambiente %d"), ContractionFactor, (int32)EnvironmentType);

    // Aplicar contração aos atores gerados
    FVector MapCenter = FVector::ZeroVector; // Centro do mapa

    for (AActor* GeneratedActor : GeneratedActors)
    {
        if (IsValid(GeneratedActor))
        {
            FVector OriginalLocation = GeneratedActor->GetActorLocation();
            FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
            FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

            // Preservar altura original
            NewLocation.Z = OriginalLocation.Z;

            GeneratedActor->SetActorLocation(NewLocation);
        }
    }

    // Aplicar contração específica baseada no tipo de ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Contrair florestas respirantes
            for (FBreathingForestData& ForestData : BreathingForestData)
            {
                FVector DirectionToCenter = (MapCenter - ForestData.Center).GetSafeNormal();
                ForestData.Center = ForestData.Center + DirectionToCenter * (1.0f - ContractionFactor) * ForestData.Center.Size2D();

                // Contrair posições das árvores
                for (FVector& TreePosition : ForestData.TreePositions)
                {
                    FVector TreeDirectionToCenter = (MapCenter - TreePosition).GetSafeNormal();
                    TreePosition = TreePosition + TreeDirectionToCenter * (1.0f - ContractionFactor) * TreePosition.Size2D();
                }
            }
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Contrair ilhas flutuantes
            for (FFloatingIslandData& IslandData : FloatingIslandData)
            {
                if (IsValid(IslandData.IslandActor))
                {
                    FVector OriginalLocation = IslandData.IslandActor->GetActorLocation();
                    FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
                    FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

                    // Preservar altura original
                    NewLocation.Z = OriginalLocation.Z;

                    IslandData.IslandActor->SetActorLocation(NewLocation);
                }
            }

            // Contrair fortalezas de nuvem
            for (FCloudFortressData& FortressData : CloudFortressData)
            {
                FVector DirectionToCenter = (MapCenter - FortressData.OriginalPosition).GetSafeNormal();
                FortressData.OriginalPosition = FortressData.OriginalPosition + DirectionToCenter * (1.0f - ContractionFactor) * FortressData.OriginalPosition.Size2D();

                if (IsValid(FortressData.FortressActor))
                {
                    FortressData.FortressActor->SetActorLocation(FortressData.OriginalPosition);
                }
            }
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Contrair rios de almas
            for (FRiverOfSoulsData& RiverData : RiverOfSoulsData)
            {
                for (FVector& FlowPoint : RiverData.FlowPoints)
                {
                    FVector DirectionToCenter = (MapCenter - FlowPoint).GetSafeNormal();
                    FlowPoint = FlowPoint + DirectionToCenter * (1.0f - ContractionFactor) * FlowPoint.Size2D();
                }

                if (IsValid(RiverData.RiverActor))
                {
                    FVector OriginalLocation = RiverData.RiverActor->GetActorLocation();
                    FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
                    FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

                    // Preservar altura original
                    NewLocation.Z = OriginalLocation.Z;

                    RiverData.RiverActor->SetActorLocation(NewLocation);
                }
            }

            // Contrair estruturas fragmentadas
            for (FFragmentedStructureData& StructureData : FragmentedStructureData)
            {
                FVector DirectionToCenter = (MapCenter - StructureData.OriginalPosition).GetSafeNormal();
                StructureData.OriginalPosition = StructureData.OriginalPosition + DirectionToCenter * (1.0f - ContractionFactor) * StructureData.OriginalPosition.Size2D();

                if (IsValid(StructureData.StructureActor))
                {
                    StructureData.StructureActor->SetActorLocation(StructureData.OriginalPosition);
                }
            }

            // Contrair nexos sombrios
            for (FShadowNexusData& NexusData : ShadowNexusData)
            {
                FVector DirectionToCenter = (MapCenter - NexusData.Position).GetSafeNormal();
                NexusData.Position = NexusData.Position + DirectionToCenter * (1.0f - ContractionFactor) * NexusData.Position.Size2D();

                if (IsValid(NexusData.NexusActor))
                {
                    NexusData.NexusActor->SetActorLocation(NexusData.Position);
                }
            }
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("OnMapContraction - Contração aplicada com sucesso ao ambiente"));
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE BOUNDARY EFFECTS - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGEnvironment::SetBoundaryBlurStrength(float BlurStrength)
{
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetBoundaryBlurStrength - BoundaryPostProcessComponent is null"));
        return;
    }

    // Usar APIs modernas do UE 5.6 para configurar blur
    BlurStrength = FMath::Clamp(BlurStrength, 0.0f, 10.0f);

    // Configurar depth of field blur como alternativa moderna
    BoundaryPostProcessComponent->Settings.bOverride_DepthOfFieldFocalDistance = true;
    BoundaryPostProcessComponent->Settings.DepthOfFieldFocalDistance = 1000.0f / (BlurStrength + 0.1f);

    BoundaryPostProcessComponent->Settings.bOverride_DepthOfFieldDepthBlurAmount = true;
    BoundaryPostProcessComponent->Settings.DepthOfFieldDepthBlurAmount = BlurStrength;

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetBoundaryBlurStrength - Blur strength set to %.2f"), BlurStrength);
}

void AAURACRONPCGEnvironment::SetBoundaryBlurRadius(float BlurRadius)
{
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetBoundaryBlurRadius - BoundaryPostProcessComponent is null"));
        return;
    }

    // Usar APIs modernas do UE 5.6 para configurar raio do blur
    BlurRadius = FMath::Clamp(BlurRadius, 0.0f, 5000.0f);

    // Configurar blend radius do post process
    BoundaryPostProcessComponent->BlendRadius = BlurRadius;

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetBoundaryBlurRadius - Blur radius set to %.2f"), BlurRadius);
}

void AAURACRONPCGEnvironment::EnableBoundaryBlur(bool bEnable)
{
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::EnableBoundaryBlur - BoundaryPostProcessComponent is null"));
        return;
    }

    // Habilitar/desabilitar o componente de post process usando APIs modernas do UE 5.6
    BoundaryPostProcessComponent->SetVisibility(bEnable);
    BoundaryPostProcessComponent->BlendWeight = bEnable ? 1.0f : 0.0f;

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableBoundaryBlur - Boundary blur %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetBoundaryGradientStrength(float GradientStrength)
{
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetBoundaryGradientStrength - BoundaryPostProcessComponent is null"));
        return;
    }

    // Usar APIs modernas do UE 5.6 para configurar gradiente
    GradientStrength = FMath::Clamp(GradientStrength, 0.0f, 2.0f);

    // Configurar vignette como alternativa moderna para gradiente
    BoundaryPostProcessComponent->Settings.bOverride_VignetteIntensity = true;
    BoundaryPostProcessComponent->Settings.VignetteIntensity = GradientStrength;

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetBoundaryGradientStrength - Gradient strength set to %.2f"), GradientStrength);
}

void AAURACRONPCGEnvironment::SetBoundaryBlurColor(const FLinearColor& BlurColor)
{
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetBoundaryBlurColor - BoundaryPostProcessComponent is null"));
        return;
    }

    // Usar APIs modernas do UE 5.6 para configurar cor
    // Configurar color grading para alterar a cor geral
    BoundaryPostProcessComponent->Settings.bOverride_ColorSaturation = true;
    BoundaryPostProcessComponent->Settings.ColorSaturation = FVector4(BlurColor.R, BlurColor.G, BlurColor.B, BlurColor.A);

    BoundaryPostProcessComponent->Settings.bOverride_ColorGamma = true;
    BoundaryPostProcessComponent->Settings.ColorGamma = FVector4(BlurColor.R, BlurColor.G, BlurColor.B, BlurColor.A);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetBoundaryBlurColor - Blur color set to R:%.2f G:%.2f B:%.2f A:%.2f"),
           BlurColor.R, BlurColor.G, BlurColor.B, BlurColor.A);
}

void AAURACRONPCGEnvironment::SetBoundaryBlurType(EEnvironmentBlurType BlurType)
{
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetBoundaryBlurType - BoundaryPostProcessComponent is null"));
        return;
    }

    // Configurar diferentes tipos de blur usando APIs modernas do UE 5.6
    switch (BlurType)
    {
        case EEnvironmentBlurType::Gaussian:
            // Usar bloom para simular blur gaussiano
            BoundaryPostProcessComponent->Settings.bOverride_BloomIntensity = true;
            BoundaryPostProcessComponent->Settings.BloomIntensity = 1.0f;
            break;

        case EEnvironmentBlurType::Atmospheric:
            // Usar bloom mais intenso para efeito atmosférico
            BoundaryPostProcessComponent->Settings.bOverride_BloomIntensity = true;
            BoundaryPostProcessComponent->Settings.BloomIntensity = 1.5f;
            break;

        case EEnvironmentBlurType::Spectral:
            // Usar aberração cromática para efeito espectral
            BoundaryPostProcessComponent->Settings.bOverride_ChromaticAberrationStartOffset = true;
            BoundaryPostProcessComponent->Settings.ChromaticAberrationStartOffset = 0.1f;
            break;

        case EEnvironmentBlurType::Radial:
            // Usar vignette para simular blur radial
            BoundaryPostProcessComponent->Settings.bOverride_VignetteIntensity = true;
            BoundaryPostProcessComponent->Settings.VignetteIntensity = 1.0f;
            break;

        default:
            // None - desabilitar efeitos
            BoundaryPostProcessComponent->Settings.bOverride_BloomIntensity = false;
            break;
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetBoundaryBlurType - Blur type set to %d"), (int32)BlurType);
}

void AAURACRONPCGEnvironment::EnableBoundaryParticles(bool bEnable)
{
    // Implementação robusta usando Niagara System para partículas de boundary
    if (bEnable)
    {
        // Buscar ou criar sistema de partículas Niagara para boundary
        if (!BoundaryParticleSystem)
        {
            // Tentar carregar sistema de partículas padrão
            BoundaryParticleSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_GPUSprites.NS_GPUSprites"));
        }

        if (BoundaryParticleSystem)
        {
            // Criar componente Niagara se não existir
            if (!BoundaryParticleComponent)
            {
                BoundaryParticleComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    BoundaryParticleSystem,
                    GetActorLocation()
                );
            }

            if (BoundaryParticleComponent)
            {
                BoundaryParticleComponent->SetVisibility(true);
                BoundaryParticleComponent->Activate();
            }
        }
    }
    else
    {
        // Desabilitar partículas
        if (BoundaryParticleComponent)
        {
            BoundaryParticleComponent->SetVisibility(false);
            BoundaryParticleComponent->Deactivate();
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableBoundaryParticles - Boundary particles %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetBoundaryParticleIntensity(float Intensity)
{
    if (!BoundaryParticleComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetBoundaryParticleIntensity - BoundaryParticleComponent is null"));
        return;
    }

    // Configurar intensidade das partículas usando APIs modernas do UE 5.6
    Intensity = FMath::Clamp(Intensity, 0.0f, 10.0f);

    // Configurar parâmetros do sistema Niagara
    BoundaryParticleComponent->SetFloatParameter(TEXT("SpawnRate"), Intensity * 100.0f);
    BoundaryParticleComponent->SetFloatParameter(TEXT("Intensity"), Intensity);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetBoundaryParticleIntensity - Particle intensity set to %.2f"), Intensity);
}

void AAURACRONPCGEnvironment::EnableSpaceDistortion(bool bEnable)
{
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::EnableSpaceDistortion - BoundaryPostProcessComponent is null"));
        return;
    }

    // Implementação robusta usando Screen Space Distortion do UE 5.6
    if (bEnable)
    {
        // Usar aberração cromática para simular distorção espacial
        BoundaryPostProcessComponent->Settings.bOverride_ChromaticAberrationStartOffset = true;
        BoundaryPostProcessComponent->Settings.ChromaticAberrationStartOffset = 0.1f;

        // Usar film grain para adicionar efeito de distorção
        BoundaryPostProcessComponent->Settings.bOverride_FilmGrainIntensity = true;
        BoundaryPostProcessComponent->Settings.FilmGrainIntensity = 0.2f;
    }
    else
    {
        // Desabilitar distorção
        BoundaryPostProcessComponent->Settings.bOverride_ChromaticAberrationStartOffset = false;
        BoundaryPostProcessComponent->Settings.bOverride_FilmGrainIntensity = false;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableSpaceDistortion - Space distortion %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetSpaceDistortionStrength(float Strength)
{
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetSpaceDistortionStrength - BoundaryPostProcessComponent is null"));
        return;
    }

    // Configurar intensidade da distorção espacial usando APIs modernas do UE 5.6
    Strength = FMath::Clamp(Strength, 0.0f, 1.0f);

    // Aplicar distorção usando aberração cromática
    BoundaryPostProcessComponent->Settings.bOverride_ChromaticAberrationStartOffset = true;
    BoundaryPostProcessComponent->Settings.ChromaticAberrationStartOffset = Strength * 0.2f;

    // Aplicar film grain proporcional para efeito adicional
    BoundaryPostProcessComponent->Settings.bOverride_FilmGrainIntensity = true;
    BoundaryPostProcessComponent->Settings.FilmGrainIntensity = Strength * 0.3f;

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetSpaceDistortionStrength - Distortion strength set to %.2f"), Strength);
}

void AAURACRONPCGEnvironment::UpdateBoundaryEffects()
{
    // Implementação robusta para atualizar todos os efeitos de boundary
    if (!BoundaryPostProcessComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::UpdateBoundaryEffects - BoundaryPostProcessComponent is null"));
        return;
    }

    // Forçar atualização do componente de post process
    BoundaryPostProcessComponent->MarkRenderStateDirty();

    // Atualizar sistema de partículas se existir
    if (BoundaryParticleComponent && BoundaryParticleComponent->IsActive())
    {
        BoundaryParticleComponent->ReinitializeSystem();
    }

    // Aplicar configurações baseadas no tipo de ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Efeitos dourados e brilhantes
            SetBoundaryBlurColor(FLinearColor(1.0f, 0.8f, 0.3f, 1.0f));
            SetBoundaryBlurType(EEnvironmentBlurType::Atmospheric);
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Efeitos azuis e etéreos
            SetBoundaryBlurColor(FLinearColor(0.3f, 0.7f, 1.0f, 1.0f));
            SetBoundaryBlurType(EEnvironmentBlurType::Gaussian);
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Efeitos sombrios e espectrais
            SetBoundaryBlurColor(FLinearColor(0.5f, 0.2f, 0.8f, 1.0f));
            SetBoundaryBlurType(EEnvironmentBlurType::Spectral);
            EnableSpaceDistortion(true);
            SetSpaceDistortionStrength(0.3f);
            break;
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::UpdateBoundaryEffects - Boundary effects updated for environment type %d"), (int32)EnvironmentType);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGEnvironment::SetHighEndDevice(bool bIsHighEnd)
{
    // Implementação robusta para configurar dispositivo high-end
    bIsHighEndDevice = bIsHighEnd;

    if (bIsHighEnd)
    {
        // Configurações para dispositivos high-end
        MaxParticleCount = 5000;
        LightingQuality = 3; // Ultra
        ShadowQuality = 3; // Ultra
        EffectQuality = 3; // Ultra
    }
    else
    {
        // Configurações para dispositivos low-end
        MaxParticleCount = 1000;
        LightingQuality = 1; // Low
        ShadowQuality = 1; // Low
        EffectQuality = 1; // Low
    }

    // Aplicar configurações imediatamente
    UpdateRenderingQuality();

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::SetHighEndDevice - High-end device: %s"), bIsHighEnd ? TEXT("true") : TEXT("false"));
}

void AAURACRONPCGEnvironment::UpdateRenderingQuality()
{
    // Implementação robusta para atualizar qualidade de renderização

    // Atualizar qualidade das partículas Niagara
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("MaxParticles"), (float)MaxParticleCount);
            NiagaraComp->SetFloatParameter(TEXT("QualityLevel"), (float)EffectQuality);
        }
    }

    // Atualizar qualidade das luzes
    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(LightComp))
        {
            LightComp->SetCastShadows(ShadowQuality > 1);
            LightComp->SetIntensity(LightComp->Intensity * (LightingQuality * 0.5f));
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::UpdateRenderingQuality - Rendering quality updated"));
}

void AAURACRONPCGEnvironment::SetEnvironmentIntensity(float Intensity)
{
    // Implementação robusta para definir intensidade do ambiente
    EnvironmentIntensity = FMath::Clamp(Intensity, 0.0f, 10.0f);

    // Aplicar intensidade a todos os componentes visuais
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("Intensity"), EnvironmentIntensity);
            NiagaraComp->SetFloatParameter(TEXT("EnvironmentIntensity"), EnvironmentIntensity);
        }
    }

    // Aplicar intensidade às luzes
    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(LightComp))
        {
            LightComp->SetIntensity(LightComp->Intensity * EnvironmentIntensity);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetEnvironmentIntensity - Intensity set to %.2f"), EnvironmentIntensity);
}

float AAURACRONPCGEnvironment::GetEnvironmentIntensity() const
{
    return EnvironmentIntensity;
}

void AAURACRONPCGEnvironment::SetTransitionSpeed(float Speed)
{
    // Implementação robusta para definir velocidade de transição
    TransitionSpeed = FMath::Clamp(Speed, 0.1f, 10.0f);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetTransitionSpeed - Speed set to %.2f"), TransitionSpeed);
}

void AAURACRONPCGEnvironment::SetBlendRadius(float Radius)
{
    // Implementação robusta para definir raio de blend
    BlendRadius = FMath::Clamp(Radius, 100.0f, 10000.0f);

    // Aplicar ao componente de post process
    if (BoundaryPostProcessComponent && IsValid(BoundaryPostProcessComponent))
    {
        BoundaryPostProcessComponent->BlendRadius = BlendRadius;
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetBlendRadius - Radius set to %.2f"), BlendRadius);
}

void AAURACRONPCGEnvironment::SetEmergenceRate(float Rate)
{
    // Implementação robusta para definir taxa de emergência
    EmergenceRate = FMath::Clamp(Rate, 0.0f, 5.0f);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetEmergenceRate - Rate set to %.2f"), EmergenceRate);
}

void AAURACRONPCGEnvironment::StartGradualEmergence()
{
    // Implementação robusta para iniciar emergência gradual
    bIsEmergingGradually = true;
    EmergenceStartTime = GetWorld()->GetTimeSeconds();

    // Configurar timer para emergência gradual
    GetWorld()->GetTimerManager().SetTimer(
        EmergenceTimerHandle,
        this,
        &AAURACRONPCGEnvironment::UpdateGradualEmergence,
        0.1f, // A cada 0.1 segundos
        true // Repetir
    );

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::StartGradualEmergence - Gradual emergence started"));
}

void AAURACRONPCGEnvironment::UpdateGradualEmergence()
{
    // Implementação robusta para atualizar emergência gradual
    if (!bIsEmergingGradually)
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    float ElapsedTime = CurrentTime - EmergenceStartTime;
    float EmergenceDuration = 10.0f / EmergenceRate; // Duração baseada na taxa

    if (ElapsedTime >= EmergenceDuration)
    {
        // Emergência completa
        bIsEmergingGradually = false;
        ActivityScale = 1.0f;

        // Parar o timer
        GetWorld()->GetTimerManager().ClearTimer(EmergenceTimerHandle);

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::UpdateGradualEmergence - Emergence completed"));
    }
    else
    {
        // Atualizar escala de atividade gradualmente
        float Progress = ElapsedTime / EmergenceDuration;
        ActivityScale = FMath::Lerp(0.0f, 1.0f, Progress);

        // Aplicar escala aos componentes visuais
        TArray<UNiagaraComponent*> NiagaraComponents;
        GetComponents<UNiagaraComponent>(NiagaraComponents);

        for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
        {
            if (NiagaraComp && IsValid(NiagaraComp))
            {
                NiagaraComp->SetFloatParameter(TEXT("ActivityScale"), ActivityScale);
            }
        }
    }
}

// ========================================
// IMPLEMENTAÇÃO DE TODAS AS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGEnvironment::SetLightIntensity(float Intensity)
{
    // Implementação robusta para definir intensidade da luz
    Intensity = FMath::Clamp(Intensity, 0.0f, 100.0f);

    // Aplicar a todos os componentes de luz
    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(LightComp))
        {
            LightComp->SetIntensity(Intensity);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetLightIntensity - Light intensity set to %.2f"), Intensity);
}

void AAURACRONPCGEnvironment::SetParticleSpawnRate(float Rate)
{
    // Implementação robusta para definir taxa de spawn de partículas
    Rate = FMath::Clamp(Rate, 0.0f, 10000.0f);

    // Aplicar a todos os componentes Niagara
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("SpawnRate"), Rate);
            NiagaraComp->SetFloatParameter(TEXT("ParticleSpawnRate"), Rate);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetParticleSpawnRate - Particle spawn rate set to %.2f"), Rate);
}

void AAURACRONPCGEnvironment::SetWindStrength(float Strength)
{
    // Implementação robusta para definir força do vento
    Strength = FMath::Clamp(Strength, 0.0f, 10.0f);

    // Aplicar aos sistemas de partículas
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("WindStrength"), Strength);
            NiagaraComp->SetVectorParameter(TEXT("WindDirection"), FVector(Strength, 0.0f, 0.0f));
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetWindStrength - Wind strength set to %.2f"), Strength);
}

void AAURACRONPCGEnvironment::SetCloudDensity(float Density)
{
    // Implementação robusta para definir densidade das nuvens
    Density = FMath::Clamp(Density, 0.0f, 1.0f);

    // Aplicar aos sistemas de nuvens
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("CloudDensity"), Density);
            NiagaraComp->SetFloatParameter(TEXT("AtmosphericDensity"), Density);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetCloudDensity - Cloud density set to %.2f"), Density);
}

void AAURACRONPCGEnvironment::SetShadowIntensity(float Intensity)
{
    // Implementação robusta para definir intensidade das sombras
    Intensity = FMath::Clamp(Intensity, 0.0f, 2.0f);

    // Aplicar a todos os componentes de luz
    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(LightComp))
        {
            if (UDirectionalLightComponent* DirLight = Cast<UDirectionalLightComponent>(LightComp))
            {
                DirLight->SetCastShadows(Intensity > 0.0f);
                // SetShadowResolutionScale não existe no UE 5.6, usar alternativa
                DirLight->SetIntensity(DirLight->Intensity * Intensity);
            }
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetShadowIntensity - Shadow intensity set to %.2f"), Intensity);
}

void AAURACRONPCGEnvironment::SetSpectralActivity(float Activity)
{
    // Implementação robusta para definir atividade espectral
    Activity = FMath::Clamp(Activity, 0.0f, 5.0f);

    // Aplicar aos efeitos espectrais
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("SpectralActivity"), Activity);
            NiagaraComp->SetFloatParameter(TEXT("GhostlyIntensity"), Activity);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetSpectralActivity - Spectral activity set to %.2f"), Activity);
}

void AAURACRONPCGEnvironment::ApplyPhaseConfiguration(EAURACRONMapPhase Phase)
{
    // Implementação robusta para aplicar configuração de fase
    CurrentMapPhase = Phase;

    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            SetLightIntensity(1.0f);
            SetParticleSpawnRate(100.0f);
            SetWindStrength(1.0f);
            SetCloudDensity(0.3f);
            SetShadowIntensity(0.5f);
            SetSpectralActivity(0.2f);
            break;

        case EAURACRONMapPhase::Convergence:
            SetLightIntensity(1.5f);
            SetParticleSpawnRate(200.0f);
            SetWindStrength(2.0f);
            SetCloudDensity(0.5f);
            SetShadowIntensity(0.8f);
            SetSpectralActivity(0.5f);
            break;

        case EAURACRONMapPhase::Intensification:
            SetLightIntensity(2.0f);
            SetParticleSpawnRate(400.0f);
            SetWindStrength(3.0f);
            SetCloudDensity(0.7f);
            SetShadowIntensity(1.2f);
            SetSpectralActivity(1.0f);
            break;

        case EAURACRONMapPhase::Resolution:
            SetLightIntensity(3.0f);
            SetParticleSpawnRate(800.0f);
            SetWindStrength(5.0f);
            SetCloudDensity(0.9f);
            SetShadowIntensity(1.5f);
            SetSpectralActivity(2.0f);
            break;
    }

    // Atualizar efeitos visuais
    UpdateVisualEffects();

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::ApplyPhaseConfiguration - Phase configuration applied for phase %d"), (int32)Phase);
}

void AAURACRONPCGEnvironment::UpdateVisualEffects()
{
    // Implementação robusta para atualizar efeitos visuais

    // Atualizar todos os componentes Niagara
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->ReinitializeSystem();
            NiagaraComp->SetFloatParameter(TEXT("EnvironmentType"), (float)(int32)EnvironmentType);
            NiagaraComp->SetFloatParameter(TEXT("MapPhase"), (float)(int32)CurrentMapPhase);
            NiagaraComp->SetFloatParameter(TEXT("ActivityScale"), ActivityScale);
        }
    }

    // Atualizar componentes de luz
    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(LightComp))
        {
            LightComp->MarkRenderStateDirty();
        }
    }

    // Atualizar efeitos de boundary
    UpdateBoundaryEffects();

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::UpdateVisualEffects - Visual effects updated"));
}

EAURACRONMapPhase AAURACRONPCGEnvironment::GetCurrentMapPhase() const
{
    return CurrentMapPhase;
}

void AAURACRONPCGEnvironment::SetCurrentMapPhase(EAURACRONMapPhase NewPhase)
{
    if (CurrentMapPhase != NewPhase)
    {
        EAURACRONMapPhase OldPhase = CurrentMapPhase;
        CurrentMapPhase = NewPhase;

        // Aplicar configuração da nova fase
        ApplyPhaseConfiguration(NewPhase);

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::SetCurrentMapPhase - Phase changed from %d to %d"), (int32)OldPhase, (int32)NewPhase);
    }
}

bool AAURACRONPCGEnvironment::HasPhaseManagerReference() const
{
    return PhaseManagerReference != nullptr && IsValid(Cast<UObject>(PhaseManagerReference));
}

void AAURACRONPCGEnvironment::SetPhaseManagerReference(AAURACRONPCGPhaseManager* PhaseManager)
{
    if (PhaseManager && IsValid(Cast<UObject>(PhaseManager)))
    {
        PhaseManagerReference = PhaseManager;
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::SetPhaseManagerReference - Phase manager reference set"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPhaseManagerReference - Invalid phase manager"));
    }
}

void AAURACRONPCGEnvironment::EnableEnvironmentBlending(bool bEnable)
{
    // Implementação robusta para habilitar blending de ambiente
    bEnvironmentBlendingEnabled = bEnable;

    if (BoundaryPostProcessComponent && IsValid(BoundaryPostProcessComponent))
    {
        BoundaryPostProcessComponent->SetVisibility(bEnable);
        BoundaryPostProcessComponent->BlendWeight = bEnable ? 1.0f : 0.0f;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableEnvironmentBlending - Environment blending %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetBlendingStrength(float Strength)
{
    // Implementação robusta para definir força do blending
    Strength = FMath::Clamp(Strength, 0.0f, 2.0f);
    BlendingStrength = Strength;

    if (BoundaryPostProcessComponent && IsValid(BoundaryPostProcessComponent))
    {
        BoundaryPostProcessComponent->BlendWeight = Strength;
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetBlendingStrength - Blending strength set to %.2f"), Strength);
}

void AAURACRONPCGEnvironment::EnableAdvancedTransitions(bool bEnable)
{
    // Implementação robusta para habilitar transições avançadas
    bAdvancedTransitionsEnabled = bEnable;

    if (bEnable)
    {
        // Configurar transições mais complexas
        SetTransitionSpeed(2.0f);
        SetBlendingStrength(1.5f);
        EnableEnvironmentBlending(true);
    }
    else
    {
        // Configurar transições simples
        SetTransitionSpeed(1.0f);
        SetBlendingStrength(1.0f);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableAdvancedTransitions - Advanced transitions %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetTransitionComplexity(float Complexity)
{
    // Implementação robusta para definir complexidade da transição
    Complexity = FMath::Clamp(Complexity, 0.1f, 5.0f);
    TransitionComplexity = Complexity;

    // Aplicar complexidade aos efeitos
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("TransitionComplexity"), Complexity);
            NiagaraComp->SetFloatParameter(TEXT("EffectComplexity"), Complexity);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetTransitionComplexity - Transition complexity set to %.2f"), Complexity);
}

void AAURACRONPCGEnvironment::EnableInteractiveElements(bool bEnable)
{
    // Implementação robusta para habilitar elementos interativos
    bInteractiveElementsEnabled = bEnable;

    // Encontrar e configurar elementos interativos
    TArray<UStaticMeshComponent*> MeshComponents;
    GetComponents<UStaticMeshComponent>(MeshComponents);

    for (UStaticMeshComponent* MeshComp : MeshComponents)
    {
        if (MeshComp && IsValid(MeshComp))
        {
            // Habilitar/desabilitar colisão para interação
            MeshComp->SetCollisionEnabled(bEnable ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);

            // Configurar material para indicar interatividade
            if (UMaterialInterface* Material = MeshComp->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0);
                if (DynamicMaterial)
                {
                    DynamicMaterial->SetScalarParameterValue(TEXT("Interactive"), bEnable ? 1.0f : 0.0f);
                    DynamicMaterial->SetScalarParameterValue(TEXT("Glow"), bEnable ? 0.5f : 0.0f);
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableInteractiveElements - Interactive elements %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetInteractionRadius(float Radius)
{
    // Implementação robusta para definir raio de interação
    Radius = FMath::Clamp(Radius, 100.0f, 5000.0f);
    InteractionRadius = Radius;

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetInteractionRadius - Interaction radius set to %.2f"), Radius);
}

void AAURACRONPCGEnvironment::EnableLightBlending(bool bEnable)
{
    // Implementação robusta para habilitar blending de luz
    bLightBlendingEnabled = bEnable;

    TArray<ULightComponent*> LightComponents;
    GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(LightComp))
        {
            if (bEnable)
            {
                // Habilitar blending suave de luz
                LightComp->SetIntensity(LightComp->Intensity * 1.2f);
                LightComp->SetTemperature(LightComp->Temperature + 500.0f);
            }
            else
            {
                // Restaurar configurações normais
                LightComp->SetIntensity(LightComp->Intensity / 1.2f);
                LightComp->SetTemperature(LightComp->Temperature - 500.0f);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableLightBlending - Light blending %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::EnableWindTransitions(bool bEnable)
{
    // Implementação robusta para habilitar transições de vento
    bWindTransitionsEnabled = bEnable;

    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("WindTransitions"), bEnable ? 1.0f : 0.0f);
            NiagaraComp->SetFloatParameter(TEXT("WindVariation"), bEnable ? 2.0f : 0.5f);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableWindTransitions - Wind transitions %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::EnableSpectralInteractions(bool bEnable)
{
    // Implementação robusta para habilitar interações espectrais
    bSpectralInteractionsEnabled = bEnable;

    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("SpectralInteractions"), bEnable ? 1.0f : 0.0f);
            NiagaraComp->SetFloatParameter(TEXT("GhostlyEffects"), bEnable ? 1.5f : 0.0f);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableSpectralInteractions - Spectral interactions %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::EnableSmoothTransitions(bool bEnable)
{
    // Implementação robusta para habilitar transições suaves
    bSmoothTransitionsEnabled = bEnable;

    if (bEnable)
    {
        SetTransitionSpeed(0.5f); // Mais lento para suavidade
        SetBlendingStrength(0.8f); // Blending mais suave
    }
    else
    {
        SetTransitionSpeed(2.0f); // Mais rápido
        SetBlendingStrength(1.5f); // Blending mais forte
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableSmoothTransitions - Smooth transitions %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetAltitudeEffect(float Effect)
{
    // Implementação robusta para definir efeito de altitude
    Effect = FMath::Clamp(Effect, 0.0f, 5.0f);
    AltitudeEffect = Effect;

    // Aplicar efeito baseado na altitude
    FVector CurrentLocation = GetActorLocation();
    float AltitudeMultiplier = (CurrentLocation.Z / 1000.0f) * Effect;

    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("AltitudeEffect"), Effect);
            NiagaraComp->SetFloatParameter(TEXT("AltitudeMultiplier"), AltitudeMultiplier);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetAltitudeEffect - Altitude effect set to %.2f"), Effect);
}

void AAURACRONPCGEnvironment::SetTransitionGradient(float Gradient)
{
    // Implementação robusta para definir gradiente de transição
    Gradient = FMath::Clamp(Gradient, 0.1f, 3.0f);
    TransitionGradient = Gradient;

    if (BoundaryPostProcessComponent && IsValid(BoundaryPostProcessComponent))
    {
        BoundaryPostProcessComponent->Settings.bOverride_VignetteIntensity = true;
        BoundaryPostProcessComponent->Settings.VignetteIntensity = Gradient;
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetTransitionGradient - Transition gradient set to %.2f"), Gradient);
}

void AAURACRONPCGEnvironment::EnableHeightBasedTransition(bool bEnable)
{
    // Implementação robusta para habilitar transição baseada em altura
    bHeightBasedTransitionEnabled = bEnable;

    if (bEnable)
    {
        // Configurar transições baseadas na altura do terreno
        FVector CurrentLocation = GetActorLocation();
        float HeightFactor = CurrentLocation.Z / 1000.0f; // Normalizar altura

        SetTransitionGradient(1.0f + HeightFactor);
        SetAltitudeEffect(HeightFactor * 2.0f);
    }
    else
    {
        SetTransitionGradient(1.0f);
        SetAltitudeEffect(0.0f);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableHeightBasedTransition - Height-based transition %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetParticleTransitionRate(float Rate)
{
    // Implementação robusta para definir taxa de transição de partículas
    Rate = FMath::Clamp(Rate, 0.1f, 10.0f);
    ParticleTransitionRate = Rate;

    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("TransitionRate"), Rate);
            NiagaraComp->SetFloatParameter(TEXT("ParticleTransitionSpeed"), Rate);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetParticleTransitionRate - Particle transition rate set to %.2f"), Rate);
}

void AAURACRONPCGEnvironment::EnableVolumetricClouds(bool bEnable)
{
    // Implementação robusta para habilitar nuvens volumétricas
    bVolumetricCloudsEnabled = bEnable;

    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("VolumetricClouds"), bEnable ? 1.0f : 0.0f);
            NiagaraComp->SetFloatParameter(TEXT("CloudVolumetrics"), bEnable ? 1.5f : 0.0f);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableVolumetricClouds - Volumetric clouds %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetCloudTransitionSpeed(float Speed)
{
    // Implementação robusta para definir velocidade de transição das nuvens
    Speed = FMath::Clamp(Speed, 0.1f, 5.0f);
    CloudTransitionSpeed = Speed;

    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("CloudTransitionSpeed"), Speed);
            NiagaraComp->SetFloatParameter(TEXT("AtmosphericTransitionRate"), Speed);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetCloudTransitionSpeed - Cloud transition speed set to %.2f"), Speed);
}

void AAURACRONPCGEnvironment::UpdateTransitionSettings()
{
    // Implementação robusta para atualizar configurações de transição

    // Aplicar todas as configurações de transição atuais
    SetTransitionSpeed(TransitionSpeed);
    SetBlendingStrength(BlendingStrength);
    SetTransitionComplexity(TransitionComplexity);
    SetTransitionGradient(TransitionGradient);
    SetParticleTransitionRate(ParticleTransitionRate);
    SetCloudTransitionSpeed(CloudTransitionSpeed);

    // Atualizar efeitos visuais com as novas configurações
    UpdateVisualEffects();

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::UpdateTransitionSettings - Transition settings updated"));
}

void AAURACRONPCGEnvironment::SetTransitionToZephyr(bool bEnable)
{
    // Implementação robusta para definir transição para Zephyr
    bTransitionToZephyr = bEnable;

    if (bEnable)
    {
        // Configurar transição específica para Zephyr Firmament
        SetWindStrength(3.0f);
        SetCloudDensity(0.8f);
        SetAltitudeEffect(2.0f);
        EnableVolumetricClouds(true);
        SetCloudTransitionSpeed(2.0f);
    }
    else
    {
        // Restaurar configurações normais
        SetWindStrength(1.0f);
        SetCloudDensity(0.3f);
        SetAltitudeEffect(0.0f);
        EnableVolumetricClouds(false);
        SetCloudTransitionSpeed(1.0f);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::SetTransitionToZephyr - Zephyr transition %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetZephyrTransitionStrength(float Strength)
{
    // Implementação robusta para definir força da transição Zephyr
    Strength = FMath::Clamp(Strength, 0.0f, 3.0f);
    ZephyrTransitionStrength = Strength;

    if (bTransitionToZephyr)
    {
        // Aplicar força da transição aos efeitos
        SetWindStrength(1.0f + (Strength * 2.0f));
        SetCloudDensity(0.3f + (Strength * 0.5f));
        SetAltitudeEffect(Strength);
        SetCloudTransitionSpeed(1.0f + Strength);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetZephyrTransitionStrength - Zephyr transition strength set to %.2f"), Strength);
}

void AAURACRONPCGEnvironment::EnableSimultaneousMode(bool bEnable)
{
    // Implementação robusta para habilitar modo simultâneo
    bSimultaneousModeEnabled = bEnable;

    if (bEnable)
    {
        // Configurar para múltiplos ambientes simultâneos
        EnableEnvironmentBlending(true);
        SetBlendingStrength(2.0f);
        EnableAdvancedTransitions(true);
        SetTransitionComplexity(2.5f);
    }
    else
    {
        // Configurar para ambiente único
        EnableEnvironmentBlending(false);
        SetBlendingStrength(1.0f);
        EnableAdvancedTransitions(false);
        SetTransitionComplexity(1.0f);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableSimultaneousMode - Simultaneous mode %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetEnvironmentPriority(int32 Priority)
{
    // Implementação robusta para definir prioridade do ambiente
    Priority = FMath::Clamp(Priority, 0, 10);
    EnvironmentPriority = Priority;

    // Ajustar intensidade baseada na prioridade
    float PriorityMultiplier = (float)Priority / 10.0f;
    SetEnvironmentIntensity(1.0f + PriorityMultiplier);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetEnvironmentPriority - Environment priority set to %d"), Priority);
}

void AAURACRONPCGEnvironment::EnableCrossEnvironmentBlending(bool bEnable)
{
    // Implementação robusta para habilitar blending entre ambientes
    bCrossEnvironmentBlendingEnabled = bEnable;

    if (bEnable)
    {
        EnableEnvironmentBlending(true);
        SetBlendingStrength(1.5f);
        EnableAdvancedTransitions(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::EnableCrossEnvironmentBlending - Cross-environment blending %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGEnvironment::SetCrossBlendStrength(float Strength)
{
    // Implementação robusta para definir força do blending cruzado
    Strength = FMath::Clamp(Strength, 0.0f, 3.0f);
    CrossBlendStrength = Strength;

    if (bCrossEnvironmentBlendingEnabled)
    {
        SetBlendingStrength(Strength);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironment::SetCrossBlendStrength - Cross blend strength set to %.2f"), Strength);
}

void AAURACRONPCGEnvironment::UpdateTectonicBridges(float DeltaTime)
{
    // Implementação robusta para atualizar pontes tectônicas

    // Encontrar todos os componentes de mesh que representam pontes
    TArray<UStaticMeshComponent*> BridgeComponents;
    GetComponents<UStaticMeshComponent>(BridgeComponents);

    for (UStaticMeshComponent* MeshComp : BridgeComponents)
    {
        if (MeshComp && IsValid(MeshComp) && MeshComp->GetName().Contains(TEXT("Bridge")))
        {
            // Aplicar movimento tectônico sutil
            FVector CurrentLocation = MeshComp->GetComponentLocation();
            float TectonicOffset = FMath::Sin(GetWorld()->GetTimeSeconds() * 0.1f) * 2.0f;

            FVector NewLocation = CurrentLocation;
            NewLocation.Z += TectonicOffset * DeltaTime;

            MeshComp->SetWorldLocation(NewLocation);

            // Atualizar material para mostrar atividade tectônica
            if (UMaterialInterface* Material = MeshComp->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0);
                if (DynamicMaterial)
                {
                    float TectonicActivity = FMath::Abs(TectonicOffset) / 2.0f;
                    DynamicMaterial->SetScalarParameterValue(TEXT("TectonicActivity"), TectonicActivity);
                    DynamicMaterial->SetScalarParameterValue(TEXT("BridgeStability"), 1.0f - TectonicActivity);
                }
            }
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::UpdateTectonicBridges - Tectonic bridges updated"));
}
