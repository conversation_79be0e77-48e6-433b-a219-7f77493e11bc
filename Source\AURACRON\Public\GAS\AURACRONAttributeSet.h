// AURACRONAttributeSet.h
// Sistema de Sígilos AURACRON - Conjunto de Atributos UE 5.6
// Implementação robusta com GAS para atributos do personagem

#pragma once

#include "CoreMinimal.h"
#include "AttributeSet.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffectExtension.h"
#include "AURACRONAttributeSet.generated.h"

// Macros para facilitar a criação de atributos
#define ATTRIBUTE_ACCESSORS(ClassName, PropertyName) \
    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(ClassName, PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_GETTER(PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_SETTER(PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_INITTER(PropertyName)

/**
 * Conjunto de atributos para personagens AURACRON
 * Implementa todos os atributos necessários para o sistema de Sígilos
 */
UCLASS(BlueprintType)
class AURACRON_API UAURACRONAttributeSet : public UAttributeSet
{
    GENERATED_BODY()

public:
    UAURACRONAttributeSet();

    // UAttributeSet interface
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    virtual void PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue) override;
    virtual void PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data) override;

    // Atributos Primários
    /** Vida atual do personagem */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Primários", ReplicatedUsing = OnRep_Health)
    FGameplayAttributeData Health;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, Health)

    /** Vida máxima do personagem */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Primários", ReplicatedUsing = OnRep_MaxHealth)
    FGameplayAttributeData MaxHealth;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, MaxHealth)

    /** Mana atual do personagem */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Primários", ReplicatedUsing = OnRep_Mana)
    FGameplayAttributeData Mana;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, Mana)

    /** Mana máxima do personagem */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Primários", ReplicatedUsing = OnRep_MaxMana)
    FGameplayAttributeData MaxMana;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, MaxMana)

    // Atributos de Combate
    /** Dano de ataque físico */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Combate", ReplicatedUsing = OnRep_AttackDamage)
    FGameplayAttributeData AttackDamage;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, AttackDamage)

    /** Poder de habilidade (dano mágico) */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Combate", ReplicatedUsing = OnRep_AbilityPower)
    FGameplayAttributeData AbilityPower;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, AbilityPower)

    /** Armadura física */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Combate", ReplicatedUsing = OnRep_Armor)
    FGameplayAttributeData Armor;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, Armor)

    /** Resistência mágica */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Combate", ReplicatedUsing = OnRep_MagicResistance)
    FGameplayAttributeData MagicResistance;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, MagicResistance)

    /** Velocidade de ataque */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Combate", ReplicatedUsing = OnRep_AttackSpeed)
    FGameplayAttributeData AttackSpeed;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, AttackSpeed)

    /** Chance de crítico */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Combate", ReplicatedUsing = OnRep_CriticalChance)
    FGameplayAttributeData CriticalChance;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, CriticalChance)

    /** Dano crítico */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Combate", ReplicatedUsing = OnRep_CriticalDamage)
    FGameplayAttributeData CriticalDamage;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, CriticalDamage)

    /** Precisão/Acurácia */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Combate", ReplicatedUsing = OnRep_Accuracy)
    FGameplayAttributeData Accuracy;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, Accuracy)

    // Atributos de Movimento
    /** Velocidade de movimento */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Movimento", ReplicatedUsing = OnRep_MovementSpeed)
    FGameplayAttributeData MovementSpeed;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, MovementSpeed)

    // Atributos de Regeneração
    /** Regeneração de vida por segundo */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Regeneração", ReplicatedUsing = OnRep_HealthRegeneration)
    FGameplayAttributeData HealthRegeneration;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, HealthRegeneration)

    /** Regeneração de mana por segundo */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Regeneração", ReplicatedUsing = OnRep_ManaRegeneration)
    FGameplayAttributeData ManaRegeneration;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, ManaRegeneration)

    // Atributos de Cooldown
    /** Redução de cooldown (0.0 - 1.0) */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Cooldown", ReplicatedUsing = OnRep_CooldownReduction)
    FGameplayAttributeData CooldownReduction;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, CooldownReduction)

    // Atributos de Sígilos
    /** Eficiência dos Sígilos (multiplicador de efeitos) */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Sígilos", ReplicatedUsing = OnRep_SigilEfficiency)
    FGameplayAttributeData SigilEfficiency;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, SigilEfficiency)

    /** Capacidade máxima de Sígilos equipados */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Sígilos", ReplicatedUsing = OnRep_MaxSigilSlots)
    FGameplayAttributeData MaxSigilSlots;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, MaxSigilSlots)

    /** Multiplicador de experiência */
    UPROPERTY(BlueprintReadOnly, Category = "Atributos|Progressão", ReplicatedUsing = OnRep_ExperienceMultiplier)
    FGameplayAttributeData ExperienceMultiplier;
    ATTRIBUTE_ACCESSORS(UAURACRONAttributeSet, ExperienceMultiplier)

protected:
    // Funções de replicação
    UFUNCTION()
    virtual void OnRep_Health(const FGameplayAttributeData& OldHealth);

    UFUNCTION()
    virtual void OnRep_MaxHealth(const FGameplayAttributeData& OldMaxHealth);

    UFUNCTION()
    virtual void OnRep_Mana(const FGameplayAttributeData& OldMana);

    UFUNCTION()
    virtual void OnRep_MaxMana(const FGameplayAttributeData& OldMaxMana);

    UFUNCTION()
    virtual void OnRep_AttackDamage(const FGameplayAttributeData& OldAttackDamage);

    UFUNCTION()
    virtual void OnRep_AbilityPower(const FGameplayAttributeData& OldAbilityPower);

    UFUNCTION()
    virtual void OnRep_Armor(const FGameplayAttributeData& OldArmor);

    UFUNCTION()
    virtual void OnRep_MagicResistance(const FGameplayAttributeData& OldMagicResistance);

    UFUNCTION()
    virtual void OnRep_AttackSpeed(const FGameplayAttributeData& OldAttackSpeed);

    UFUNCTION()
    virtual void OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance);

    UFUNCTION()
    virtual void OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed);

    UFUNCTION()
    virtual void OnRep_HealthRegeneration(const FGameplayAttributeData& OldHealthRegeneration);

    UFUNCTION()
    virtual void OnRep_ManaRegeneration(const FGameplayAttributeData& OldManaRegeneration);

    UFUNCTION()
    virtual void OnRep_CooldownReduction(const FGameplayAttributeData& OldCooldownReduction);

    UFUNCTION()
    virtual void OnRep_SigilEfficiency(const FGameplayAttributeData& OldSigilEfficiency);

    UFUNCTION()
    virtual void OnRep_MaxSigilSlots(const FGameplayAttributeData& OldMaxSigilSlots);

    UFUNCTION()
    virtual void OnRep_CriticalDamage(const FGameplayAttributeData& OldCriticalDamage);

    UFUNCTION()
    virtual void OnRep_Accuracy(const FGameplayAttributeData& OldAccuracy);

    UFUNCTION()
    virtual void OnRep_ExperienceMultiplier(const FGameplayAttributeData& OldExperienceMultiplier);

private:
    /** Ajusta a vida quando a vida máxima muda */
    void AdjustAttributeForMaxChange(const FGameplayAttributeData& AffectedAttribute, const FGameplayAttributeData& MaxAttribute, float NewMaxValue, const FGameplayAttribute& AffectedAttributeProperty) const;
};
