// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for AURACRON
#pragma once
#include "SharedDefinitions.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h"
#undef AURACRON_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME AURACRON
#define UE_TARGET_NAME AURACRONEditor
#define AURACRON_SHIPPING 0
#define AURACRON_DEBUG 1
#define AURACRON_MAX_PLAYERS 10
#define AURACRON_FUSION_TIME 360
#define AURACRON_REFORGE_COOLDOWN 120
#define AURACRON_MAX_SIGIL_SLOTS 6
#define UE_NET_HAS_IRIS_FASTARRAY_BINDING 1
#define IRIS_PROFILING_ENABLED 0
#define UE_MODULE_NAME "AURACRON"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
#define NIAGARA_API DLLIMPORT
#define NIAGARACORE_API DLLIMPORT
#define VECTORVM_API DLLIMPORT
#define NIAGARASHADER_API DLLIMPORT
#define NIAGARAVERTEXFACTORIES_API DLLIMPORT
#define REPLICATIONGRAPH_API DLLIMPORT
#define PCGEDITOR_API DLLIMPORT
#define PLACEMENTMODE_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
#define EDITORWIDGETS_API DLLIMPORT
#define AURACRON_API DLLEXPORT
#define ENHANCEDINPUT_API DLLIMPORT
#define PCG_API DLLIMPORT
#define COMPUTEFRAMEWORK_API DLLIMPORT
#define FOLIAGE_API DLLIMPORT
#define PCGCOMPUTE_API DLLIMPORT
